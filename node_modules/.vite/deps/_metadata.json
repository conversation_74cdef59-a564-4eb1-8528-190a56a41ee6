{"hash": "d278d6f9", "configHash": "13a8ad72", "lockfileHash": "ae6688d3", "browserHash": "8aea9ad9", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "115a98e5", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "37212471", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "ed89c547", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "956448cc", "needsInterop": true}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "60a6b122", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "1c989dac", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "24f0228b", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "33e85d24", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "6ee4a4b2", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "a9c75b52", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "fd8224d3", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "34f1bcd6", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "a4ef9f39", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "6de4afff", "needsInterop": false}, "razorpay": {"src": "../../razorpay/dist/razorpay.js", "file": "razorpay.js", "fileHash": "0bb10ce1", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "36e16f45", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "fd351460", "needsInterop": false}, "sonner": {"src": "../../sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "dbf6de02", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "cbe81b48", "needsInterop": false}}, "chunks": {"browser-EMB7CRMN": {"file": "browser-EMB7CRMN.js"}, "chunk-5H4R2CZR": {"file": "chunk-5H4R2CZR.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-BOM6GUKV": {"file": "chunk-BOM6GUKV.js"}, "chunk-HRM7KPCA": {"file": "chunk-HRM7KPCA.js"}, "chunk-WYMF3RXU": {"file": "chunk-WYMF3RXU.js"}, "chunk-M7DXYTBV": {"file": "chunk-M7DXYTBV.js"}, "chunk-5PWB6DBI": {"file": "chunk-5PWB6DBI.js"}, "chunk-GKJBSOWT": {"file": "chunk-GKJBSOWT.js"}, "chunk-NRBATONI": {"file": "chunk-NRBATONI.js"}, "chunk-QJTFJ6OV": {"file": "chunk-QJTFJ6OV.js"}, "chunk-V4OQ3NZ2": {"file": "chunk-V4OQ3NZ2.js"}}}