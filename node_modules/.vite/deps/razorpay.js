import {
  __commonJS
} from "./chunk-V4OQ3NZ2.js";

// node_modules/axios/dist/browser/axios.cjs
var require_axios = __commonJS({
  "node_modules/axios/dist/browser/axios.cjs"(exports, module) {
    "use strict";
    function bind(fn, thisArg) {
      return function wrap() {
        return fn.apply(thisArg, arguments);
      };
    }
    var { toString } = Object.prototype;
    var { getPrototypeOf } = Object;
    var { iterator, toStringTag } = Symbol;
    var kindOf = /* @__PURE__ */ ((cache) => (thing) => {
      const str = toString.call(thing);
      return cache[str] || (cache[str] = str.slice(8, -1).toLowerCase());
    })(/* @__PURE__ */ Object.create(null));
    var kindOfTest = (type) => {
      type = type.toLowerCase();
      return (thing) => kindOf(thing) === type;
    };
    var typeOfTest = (type) => (thing) => typeof thing === type;
    var { isArray } = Array;
    var isUndefined = typeOfTest("undefined");
    function isBuffer(val) {
      return val !== null && !isUndefined(val) && val.constructor !== null && !isUndefined(val.constructor) && isFunction(val.constructor.isBuffer) && val.constructor.isBuffer(val);
    }
    var isArrayBuffer = kindOfTest("ArrayBuffer");
    function isArrayBufferView(val) {
      let result;
      if (typeof ArrayBuffer !== "undefined" && ArrayBuffer.isView) {
        result = ArrayBuffer.isView(val);
      } else {
        result = val && val.buffer && isArrayBuffer(val.buffer);
      }
      return result;
    }
    var isString = typeOfTest("string");
    var isFunction = typeOfTest("function");
    var isNumber = typeOfTest("number");
    var isObject = (thing) => thing !== null && typeof thing === "object";
    var isBoolean = (thing) => thing === true || thing === false;
    var isPlainObject = (val) => {
      if (kindOf(val) !== "object") {
        return false;
      }
      const prototype2 = getPrototypeOf(val);
      return (prototype2 === null || prototype2 === Object.prototype || Object.getPrototypeOf(prototype2) === null) && !(toStringTag in val) && !(iterator in val);
    };
    var isDate = kindOfTest("Date");
    var isFile = kindOfTest("File");
    var isBlob = kindOfTest("Blob");
    var isFileList = kindOfTest("FileList");
    var isStream = (val) => isObject(val) && isFunction(val.pipe);
    var isFormData = (thing) => {
      let kind;
      return thing && (typeof FormData === "function" && thing instanceof FormData || isFunction(thing.append) && ((kind = kindOf(thing)) === "formdata" || // detect form-data instance
      kind === "object" && isFunction(thing.toString) && thing.toString() === "[object FormData]"));
    };
    var isURLSearchParams = kindOfTest("URLSearchParams");
    var [isReadableStream, isRequest, isResponse, isHeaders] = ["ReadableStream", "Request", "Response", "Headers"].map(kindOfTest);
    var trim = (str) => str.trim ? str.trim() : str.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, "");
    function forEach(obj, fn, { allOwnKeys = false } = {}) {
      if (obj === null || typeof obj === "undefined") {
        return;
      }
      let i;
      let l;
      if (typeof obj !== "object") {
        obj = [obj];
      }
      if (isArray(obj)) {
        for (i = 0, l = obj.length; i < l; i++) {
          fn.call(null, obj[i], i, obj);
        }
      } else {
        const keys = allOwnKeys ? Object.getOwnPropertyNames(obj) : Object.keys(obj);
        const len = keys.length;
        let key;
        for (i = 0; i < len; i++) {
          key = keys[i];
          fn.call(null, obj[key], key, obj);
        }
      }
    }
    function findKey(obj, key) {
      key = key.toLowerCase();
      const keys = Object.keys(obj);
      let i = keys.length;
      let _key;
      while (i-- > 0) {
        _key = keys[i];
        if (key === _key.toLowerCase()) {
          return _key;
        }
      }
      return null;
    }
    var _global = (() => {
      if (typeof globalThis !== "undefined") return globalThis;
      return typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : global;
    })();
    var isContextDefined = (context) => !isUndefined(context) && context !== _global;
    function merge() {
      const { caseless } = isContextDefined(this) && this || {};
      const result = {};
      const assignValue = (val, key) => {
        const targetKey = caseless && findKey(result, key) || key;
        if (isPlainObject(result[targetKey]) && isPlainObject(val)) {
          result[targetKey] = merge(result[targetKey], val);
        } else if (isPlainObject(val)) {
          result[targetKey] = merge({}, val);
        } else if (isArray(val)) {
          result[targetKey] = val.slice();
        } else {
          result[targetKey] = val;
        }
      };
      for (let i = 0, l = arguments.length; i < l; i++) {
        arguments[i] && forEach(arguments[i], assignValue);
      }
      return result;
    }
    var extend = (a, b, thisArg, { allOwnKeys } = {}) => {
      forEach(b, (val, key) => {
        if (thisArg && isFunction(val)) {
          a[key] = bind(val, thisArg);
        } else {
          a[key] = val;
        }
      }, { allOwnKeys });
      return a;
    };
    var stripBOM = (content) => {
      if (content.charCodeAt(0) === 65279) {
        content = content.slice(1);
      }
      return content;
    };
    var inherits = (constructor, superConstructor, props, descriptors2) => {
      constructor.prototype = Object.create(superConstructor.prototype, descriptors2);
      constructor.prototype.constructor = constructor;
      Object.defineProperty(constructor, "super", {
        value: superConstructor.prototype
      });
      props && Object.assign(constructor.prototype, props);
    };
    var toFlatObject = (sourceObj, destObj, filter, propFilter) => {
      let props;
      let i;
      let prop;
      const merged = {};
      destObj = destObj || {};
      if (sourceObj == null) return destObj;
      do {
        props = Object.getOwnPropertyNames(sourceObj);
        i = props.length;
        while (i-- > 0) {
          prop = props[i];
          if ((!propFilter || propFilter(prop, sourceObj, destObj)) && !merged[prop]) {
            destObj[prop] = sourceObj[prop];
            merged[prop] = true;
          }
        }
        sourceObj = filter !== false && getPrototypeOf(sourceObj);
      } while (sourceObj && (!filter || filter(sourceObj, destObj)) && sourceObj !== Object.prototype);
      return destObj;
    };
    var endsWith = (str, searchString, position) => {
      str = String(str);
      if (position === void 0 || position > str.length) {
        position = str.length;
      }
      position -= searchString.length;
      const lastIndex = str.indexOf(searchString, position);
      return lastIndex !== -1 && lastIndex === position;
    };
    var toArray = (thing) => {
      if (!thing) return null;
      if (isArray(thing)) return thing;
      let i = thing.length;
      if (!isNumber(i)) return null;
      const arr = new Array(i);
      while (i-- > 0) {
        arr[i] = thing[i];
      }
      return arr;
    };
    var isTypedArray = /* @__PURE__ */ ((TypedArray) => {
      return (thing) => {
        return TypedArray && thing instanceof TypedArray;
      };
    })(typeof Uint8Array !== "undefined" && getPrototypeOf(Uint8Array));
    var forEachEntry = (obj, fn) => {
      const generator = obj && obj[iterator];
      const _iterator = generator.call(obj);
      let result;
      while ((result = _iterator.next()) && !result.done) {
        const pair = result.value;
        fn.call(obj, pair[0], pair[1]);
      }
    };
    var matchAll = (regExp, str) => {
      let matches;
      const arr = [];
      while ((matches = regExp.exec(str)) !== null) {
        arr.push(matches);
      }
      return arr;
    };
    var isHTMLForm = kindOfTest("HTMLFormElement");
    var toCamelCase = (str) => {
      return str.toLowerCase().replace(
        /[-_\s]([a-z\d])(\w*)/g,
        function replacer(m, p1, p2) {
          return p1.toUpperCase() + p2;
        }
      );
    };
    var hasOwnProperty = (({ hasOwnProperty: hasOwnProperty2 }) => (obj, prop) => hasOwnProperty2.call(obj, prop))(Object.prototype);
    var isRegExp = kindOfTest("RegExp");
    var reduceDescriptors = (obj, reducer) => {
      const descriptors2 = Object.getOwnPropertyDescriptors(obj);
      const reducedDescriptors = {};
      forEach(descriptors2, (descriptor, name) => {
        let ret;
        if ((ret = reducer(descriptor, name, obj)) !== false) {
          reducedDescriptors[name] = ret || descriptor;
        }
      });
      Object.defineProperties(obj, reducedDescriptors);
    };
    var freezeMethods = (obj) => {
      reduceDescriptors(obj, (descriptor, name) => {
        if (isFunction(obj) && ["arguments", "caller", "callee"].indexOf(name) !== -1) {
          return false;
        }
        const value = obj[name];
        if (!isFunction(value)) return;
        descriptor.enumerable = false;
        if ("writable" in descriptor) {
          descriptor.writable = false;
          return;
        }
        if (!descriptor.set) {
          descriptor.set = () => {
            throw Error("Can not rewrite read-only method '" + name + "'");
          };
        }
      });
    };
    var toObjectSet = (arrayOrString, delimiter) => {
      const obj = {};
      const define = (arr) => {
        arr.forEach((value) => {
          obj[value] = true;
        });
      };
      isArray(arrayOrString) ? define(arrayOrString) : define(String(arrayOrString).split(delimiter));
      return obj;
    };
    var noop = () => {
    };
    var toFiniteNumber = (value, defaultValue) => {
      return value != null && Number.isFinite(value = +value) ? value : defaultValue;
    };
    function isSpecCompliantForm(thing) {
      return !!(thing && isFunction(thing.append) && thing[toStringTag] === "FormData" && thing[iterator]);
    }
    var toJSONObject = (obj) => {
      const stack = new Array(10);
      const visit = (source, i) => {
        if (isObject(source)) {
          if (stack.indexOf(source) >= 0) {
            return;
          }
          if (!("toJSON" in source)) {
            stack[i] = source;
            const target = isArray(source) ? [] : {};
            forEach(source, (value, key) => {
              const reducedValue = visit(value, i + 1);
              !isUndefined(reducedValue) && (target[key] = reducedValue);
            });
            stack[i] = void 0;
            return target;
          }
        }
        return source;
      };
      return visit(obj, 0);
    };
    var isAsyncFn = kindOfTest("AsyncFunction");
    var isThenable = (thing) => thing && (isObject(thing) || isFunction(thing)) && isFunction(thing.then) && isFunction(thing.catch);
    var _setImmediate = ((setImmediateSupported, postMessageSupported) => {
      if (setImmediateSupported) {
        return setImmediate;
      }
      return postMessageSupported ? ((token, callbacks) => {
        _global.addEventListener("message", ({ source, data }) => {
          if (source === _global && data === token) {
            callbacks.length && callbacks.shift()();
          }
        }, false);
        return (cb) => {
          callbacks.push(cb);
          _global.postMessage(token, "*");
        };
      })(`axios@${Math.random()}`, []) : (cb) => setTimeout(cb);
    })(
      typeof setImmediate === "function",
      isFunction(_global.postMessage)
    );
    var asap = typeof queueMicrotask !== "undefined" ? queueMicrotask.bind(_global) : typeof process !== "undefined" && process.nextTick || _setImmediate;
    var isIterable = (thing) => thing != null && isFunction(thing[iterator]);
    var utils$1 = {
      isArray,
      isArrayBuffer,
      isBuffer,
      isFormData,
      isArrayBufferView,
      isString,
      isNumber,
      isBoolean,
      isObject,
      isPlainObject,
      isReadableStream,
      isRequest,
      isResponse,
      isHeaders,
      isUndefined,
      isDate,
      isFile,
      isBlob,
      isRegExp,
      isFunction,
      isStream,
      isURLSearchParams,
      isTypedArray,
      isFileList,
      forEach,
      merge,
      extend,
      trim,
      stripBOM,
      inherits,
      toFlatObject,
      kindOf,
      kindOfTest,
      endsWith,
      toArray,
      forEachEntry,
      matchAll,
      isHTMLForm,
      hasOwnProperty,
      hasOwnProp: hasOwnProperty,
      // an alias to avoid ESLint no-prototype-builtins detection
      reduceDescriptors,
      freezeMethods,
      toObjectSet,
      toCamelCase,
      noop,
      toFiniteNumber,
      findKey,
      global: _global,
      isContextDefined,
      isSpecCompliantForm,
      toJSONObject,
      isAsyncFn,
      isThenable,
      setImmediate: _setImmediate,
      asap,
      isIterable
    };
    function AxiosError(message, code, config, request, response) {
      Error.call(this);
      if (Error.captureStackTrace) {
        Error.captureStackTrace(this, this.constructor);
      } else {
        this.stack = new Error().stack;
      }
      this.message = message;
      this.name = "AxiosError";
      code && (this.code = code);
      config && (this.config = config);
      request && (this.request = request);
      if (response) {
        this.response = response;
        this.status = response.status ? response.status : null;
      }
    }
    utils$1.inherits(AxiosError, Error, {
      toJSON: function toJSON() {
        return {
          // Standard
          message: this.message,
          name: this.name,
          // Microsoft
          description: this.description,
          number: this.number,
          // Mozilla
          fileName: this.fileName,
          lineNumber: this.lineNumber,
          columnNumber: this.columnNumber,
          stack: this.stack,
          // Axios
          config: utils$1.toJSONObject(this.config),
          code: this.code,
          status: this.status
        };
      }
    });
    var prototype$1 = AxiosError.prototype;
    var descriptors = {};
    [
      "ERR_BAD_OPTION_VALUE",
      "ERR_BAD_OPTION",
      "ECONNABORTED",
      "ETIMEDOUT",
      "ERR_NETWORK",
      "ERR_FR_TOO_MANY_REDIRECTS",
      "ERR_DEPRECATED",
      "ERR_BAD_RESPONSE",
      "ERR_BAD_REQUEST",
      "ERR_CANCELED",
      "ERR_NOT_SUPPORT",
      "ERR_INVALID_URL"
      // eslint-disable-next-line func-names
    ].forEach((code) => {
      descriptors[code] = { value: code };
    });
    Object.defineProperties(AxiosError, descriptors);
    Object.defineProperty(prototype$1, "isAxiosError", { value: true });
    AxiosError.from = (error, code, config, request, response, customProps) => {
      const axiosError = Object.create(prototype$1);
      utils$1.toFlatObject(error, axiosError, function filter(obj) {
        return obj !== Error.prototype;
      }, (prop) => {
        return prop !== "isAxiosError";
      });
      AxiosError.call(axiosError, error.message, code, config, request, response);
      axiosError.cause = error;
      axiosError.name = error.name;
      customProps && Object.assign(axiosError, customProps);
      return axiosError;
    };
    var httpAdapter = null;
    function isVisitable(thing) {
      return utils$1.isPlainObject(thing) || utils$1.isArray(thing);
    }
    function removeBrackets(key) {
      return utils$1.endsWith(key, "[]") ? key.slice(0, -2) : key;
    }
    function renderKey(path, key, dots) {
      if (!path) return key;
      return path.concat(key).map(function each(token, i) {
        token = removeBrackets(token);
        return !dots && i ? "[" + token + "]" : token;
      }).join(dots ? "." : "");
    }
    function isFlatArray(arr) {
      return utils$1.isArray(arr) && !arr.some(isVisitable);
    }
    var predicates = utils$1.toFlatObject(utils$1, {}, null, function filter(prop) {
      return /^is[A-Z]/.test(prop);
    });
    function toFormData(obj, formData, options) {
      if (!utils$1.isObject(obj)) {
        throw new TypeError("target must be an object");
      }
      formData = formData || new FormData();
      options = utils$1.toFlatObject(options, {
        metaTokens: true,
        dots: false,
        indexes: false
      }, false, function defined(option, source) {
        return !utils$1.isUndefined(source[option]);
      });
      const metaTokens = options.metaTokens;
      const visitor = options.visitor || defaultVisitor;
      const dots = options.dots;
      const indexes = options.indexes;
      const _Blob = options.Blob || typeof Blob !== "undefined" && Blob;
      const useBlob = _Blob && utils$1.isSpecCompliantForm(formData);
      if (!utils$1.isFunction(visitor)) {
        throw new TypeError("visitor must be a function");
      }
      function convertValue(value) {
        if (value === null) return "";
        if (utils$1.isDate(value)) {
          return value.toISOString();
        }
        if (!useBlob && utils$1.isBlob(value)) {
          throw new AxiosError("Blob is not supported. Use a Buffer instead.");
        }
        if (utils$1.isArrayBuffer(value) || utils$1.isTypedArray(value)) {
          return useBlob && typeof Blob === "function" ? new Blob([value]) : Buffer.from(value);
        }
        return value;
      }
      function defaultVisitor(value, key, path) {
        let arr = value;
        if (value && !path && typeof value === "object") {
          if (utils$1.endsWith(key, "{}")) {
            key = metaTokens ? key : key.slice(0, -2);
            value = JSON.stringify(value);
          } else if (utils$1.isArray(value) && isFlatArray(value) || (utils$1.isFileList(value) || utils$1.endsWith(key, "[]")) && (arr = utils$1.toArray(value))) {
            key = removeBrackets(key);
            arr.forEach(function each(el, index) {
              !(utils$1.isUndefined(el) || el === null) && formData.append(
                // eslint-disable-next-line no-nested-ternary
                indexes === true ? renderKey([key], index, dots) : indexes === null ? key : key + "[]",
                convertValue(el)
              );
            });
            return false;
          }
        }
        if (isVisitable(value)) {
          return true;
        }
        formData.append(renderKey(path, key, dots), convertValue(value));
        return false;
      }
      const stack = [];
      const exposedHelpers = Object.assign(predicates, {
        defaultVisitor,
        convertValue,
        isVisitable
      });
      function build(value, path) {
        if (utils$1.isUndefined(value)) return;
        if (stack.indexOf(value) !== -1) {
          throw Error("Circular reference detected in " + path.join("."));
        }
        stack.push(value);
        utils$1.forEach(value, function each(el, key) {
          const result = !(utils$1.isUndefined(el) || el === null) && visitor.call(
            formData,
            el,
            utils$1.isString(key) ? key.trim() : key,
            path,
            exposedHelpers
          );
          if (result === true) {
            build(el, path ? path.concat(key) : [key]);
          }
        });
        stack.pop();
      }
      if (!utils$1.isObject(obj)) {
        throw new TypeError("data must be an object");
      }
      build(obj);
      return formData;
    }
    function encode$1(str) {
      const charMap = {
        "!": "%21",
        "'": "%27",
        "(": "%28",
        ")": "%29",
        "~": "%7E",
        "%20": "+",
        "%00": "\0"
      };
      return encodeURIComponent(str).replace(/[!'()~]|%20|%00/g, function replacer(match) {
        return charMap[match];
      });
    }
    function AxiosURLSearchParams(params, options) {
      this._pairs = [];
      params && toFormData(params, this, options);
    }
    var prototype = AxiosURLSearchParams.prototype;
    prototype.append = function append(name, value) {
      this._pairs.push([name, value]);
    };
    prototype.toString = function toString2(encoder) {
      const _encode = encoder ? function(value) {
        return encoder.call(this, value, encode$1);
      } : encode$1;
      return this._pairs.map(function each(pair) {
        return _encode(pair[0]) + "=" + _encode(pair[1]);
      }, "").join("&");
    };
    function encode(val) {
      return encodeURIComponent(val).replace(/%3A/gi, ":").replace(/%24/g, "$").replace(/%2C/gi, ",").replace(/%20/g, "+").replace(/%5B/gi, "[").replace(/%5D/gi, "]");
    }
    function buildURL(url, params, options) {
      if (!params) {
        return url;
      }
      const _encode = options && options.encode || encode;
      if (utils$1.isFunction(options)) {
        options = {
          serialize: options
        };
      }
      const serializeFn = options && options.serialize;
      let serializedParams;
      if (serializeFn) {
        serializedParams = serializeFn(params, options);
      } else {
        serializedParams = utils$1.isURLSearchParams(params) ? params.toString() : new AxiosURLSearchParams(params, options).toString(_encode);
      }
      if (serializedParams) {
        const hashmarkIndex = url.indexOf("#");
        if (hashmarkIndex !== -1) {
          url = url.slice(0, hashmarkIndex);
        }
        url += (url.indexOf("?") === -1 ? "?" : "&") + serializedParams;
      }
      return url;
    }
    var InterceptorManager = class {
      constructor() {
        this.handlers = [];
      }
      /**
       * Add a new interceptor to the stack
       *
       * @param {Function} fulfilled The function to handle `then` for a `Promise`
       * @param {Function} rejected The function to handle `reject` for a `Promise`
       *
       * @return {Number} An ID used to remove interceptor later
       */
      use(fulfilled, rejected, options) {
        this.handlers.push({
          fulfilled,
          rejected,
          synchronous: options ? options.synchronous : false,
          runWhen: options ? options.runWhen : null
        });
        return this.handlers.length - 1;
      }
      /**
       * Remove an interceptor from the stack
       *
       * @param {Number} id The ID that was returned by `use`
       *
       * @returns {Boolean} `true` if the interceptor was removed, `false` otherwise
       */
      eject(id) {
        if (this.handlers[id]) {
          this.handlers[id] = null;
        }
      }
      /**
       * Clear all interceptors from the stack
       *
       * @returns {void}
       */
      clear() {
        if (this.handlers) {
          this.handlers = [];
        }
      }
      /**
       * Iterate over all the registered interceptors
       *
       * This method is particularly useful for skipping over any
       * interceptors that may have become `null` calling `eject`.
       *
       * @param {Function} fn The function to call for each interceptor
       *
       * @returns {void}
       */
      forEach(fn) {
        utils$1.forEach(this.handlers, function forEachHandler(h) {
          if (h !== null) {
            fn(h);
          }
        });
      }
    };
    var InterceptorManager$1 = InterceptorManager;
    var transitionalDefaults = {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    };
    var URLSearchParams$1 = typeof URLSearchParams !== "undefined" ? URLSearchParams : AxiosURLSearchParams;
    var FormData$1 = typeof FormData !== "undefined" ? FormData : null;
    var Blob$1 = typeof Blob !== "undefined" ? Blob : null;
    var platform$1 = {
      isBrowser: true,
      classes: {
        URLSearchParams: URLSearchParams$1,
        FormData: FormData$1,
        Blob: Blob$1
      },
      protocols: ["http", "https", "file", "blob", "url", "data"]
    };
    var hasBrowserEnv = typeof window !== "undefined" && typeof document !== "undefined";
    var _navigator = typeof navigator === "object" && navigator || void 0;
    var hasStandardBrowserEnv = hasBrowserEnv && (!_navigator || ["ReactNative", "NativeScript", "NS"].indexOf(_navigator.product) < 0);
    var hasStandardBrowserWebWorkerEnv = (() => {
      return typeof WorkerGlobalScope !== "undefined" && // eslint-disable-next-line no-undef
      self instanceof WorkerGlobalScope && typeof self.importScripts === "function";
    })();
    var origin = hasBrowserEnv && window.location.href || "http://localhost";
    var utils = Object.freeze({
      __proto__: null,
      hasBrowserEnv,
      hasStandardBrowserWebWorkerEnv,
      hasStandardBrowserEnv,
      navigator: _navigator,
      origin
    });
    var platform = {
      ...utils,
      ...platform$1
    };
    function toURLEncodedForm(data, options) {
      return toFormData(data, new platform.classes.URLSearchParams(), Object.assign({
        visitor: function(value, key, path, helpers) {
          if (platform.isNode && utils$1.isBuffer(value)) {
            this.append(key, value.toString("base64"));
            return false;
          }
          return helpers.defaultVisitor.apply(this, arguments);
        }
      }, options));
    }
    function parsePropPath(name) {
      return utils$1.matchAll(/\w+|\[(\w*)]/g, name).map((match) => {
        return match[0] === "[]" ? "" : match[1] || match[0];
      });
    }
    function arrayToObject(arr) {
      const obj = {};
      const keys = Object.keys(arr);
      let i;
      const len = keys.length;
      let key;
      for (i = 0; i < len; i++) {
        key = keys[i];
        obj[key] = arr[key];
      }
      return obj;
    }
    function formDataToJSON(formData) {
      function buildPath(path, value, target, index) {
        let name = path[index++];
        if (name === "__proto__") return true;
        const isNumericKey = Number.isFinite(+name);
        const isLast = index >= path.length;
        name = !name && utils$1.isArray(target) ? target.length : name;
        if (isLast) {
          if (utils$1.hasOwnProp(target, name)) {
            target[name] = [target[name], value];
          } else {
            target[name] = value;
          }
          return !isNumericKey;
        }
        if (!target[name] || !utils$1.isObject(target[name])) {
          target[name] = [];
        }
        const result = buildPath(path, value, target[name], index);
        if (result && utils$1.isArray(target[name])) {
          target[name] = arrayToObject(target[name]);
        }
        return !isNumericKey;
      }
      if (utils$1.isFormData(formData) && utils$1.isFunction(formData.entries)) {
        const obj = {};
        utils$1.forEachEntry(formData, (name, value) => {
          buildPath(parsePropPath(name), value, obj, 0);
        });
        return obj;
      }
      return null;
    }
    function stringifySafely(rawValue, parser, encoder) {
      if (utils$1.isString(rawValue)) {
        try {
          (parser || JSON.parse)(rawValue);
          return utils$1.trim(rawValue);
        } catch (e) {
          if (e.name !== "SyntaxError") {
            throw e;
          }
        }
      }
      return (encoder || JSON.stringify)(rawValue);
    }
    var defaults = {
      transitional: transitionalDefaults,
      adapter: ["xhr", "http", "fetch"],
      transformRequest: [function transformRequest(data, headers) {
        const contentType = headers.getContentType() || "";
        const hasJSONContentType = contentType.indexOf("application/json") > -1;
        const isObjectPayload = utils$1.isObject(data);
        if (isObjectPayload && utils$1.isHTMLForm(data)) {
          data = new FormData(data);
        }
        const isFormData2 = utils$1.isFormData(data);
        if (isFormData2) {
          return hasJSONContentType ? JSON.stringify(formDataToJSON(data)) : data;
        }
        if (utils$1.isArrayBuffer(data) || utils$1.isBuffer(data) || utils$1.isStream(data) || utils$1.isFile(data) || utils$1.isBlob(data) || utils$1.isReadableStream(data)) {
          return data;
        }
        if (utils$1.isArrayBufferView(data)) {
          return data.buffer;
        }
        if (utils$1.isURLSearchParams(data)) {
          headers.setContentType("application/x-www-form-urlencoded;charset=utf-8", false);
          return data.toString();
        }
        let isFileList2;
        if (isObjectPayload) {
          if (contentType.indexOf("application/x-www-form-urlencoded") > -1) {
            return toURLEncodedForm(data, this.formSerializer).toString();
          }
          if ((isFileList2 = utils$1.isFileList(data)) || contentType.indexOf("multipart/form-data") > -1) {
            const _FormData = this.env && this.env.FormData;
            return toFormData(
              isFileList2 ? { "files[]": data } : data,
              _FormData && new _FormData(),
              this.formSerializer
            );
          }
        }
        if (isObjectPayload || hasJSONContentType) {
          headers.setContentType("application/json", false);
          return stringifySafely(data);
        }
        return data;
      }],
      transformResponse: [function transformResponse(data) {
        const transitional = this.transitional || defaults.transitional;
        const forcedJSONParsing = transitional && transitional.forcedJSONParsing;
        const JSONRequested = this.responseType === "json";
        if (utils$1.isResponse(data) || utils$1.isReadableStream(data)) {
          return data;
        }
        if (data && utils$1.isString(data) && (forcedJSONParsing && !this.responseType || JSONRequested)) {
          const silentJSONParsing = transitional && transitional.silentJSONParsing;
          const strictJSONParsing = !silentJSONParsing && JSONRequested;
          try {
            return JSON.parse(data);
          } catch (e) {
            if (strictJSONParsing) {
              if (e.name === "SyntaxError") {
                throw AxiosError.from(e, AxiosError.ERR_BAD_RESPONSE, this, null, this.response);
              }
              throw e;
            }
          }
        }
        return data;
      }],
      /**
       * A timeout in milliseconds to abort a request. If set to 0 (default) a
       * timeout is not created.
       */
      timeout: 0,
      xsrfCookieName: "XSRF-TOKEN",
      xsrfHeaderName: "X-XSRF-TOKEN",
      maxContentLength: -1,
      maxBodyLength: -1,
      env: {
        FormData: platform.classes.FormData,
        Blob: platform.classes.Blob
      },
      validateStatus: function validateStatus(status) {
        return status >= 200 && status < 300;
      },
      headers: {
        common: {
          "Accept": "application/json, text/plain, */*",
          "Content-Type": void 0
        }
      }
    };
    utils$1.forEach(["delete", "get", "head", "post", "put", "patch"], (method) => {
      defaults.headers[method] = {};
    });
    var defaults$1 = defaults;
    var ignoreDuplicateOf = utils$1.toObjectSet([
      "age",
      "authorization",
      "content-length",
      "content-type",
      "etag",
      "expires",
      "from",
      "host",
      "if-modified-since",
      "if-unmodified-since",
      "last-modified",
      "location",
      "max-forwards",
      "proxy-authorization",
      "referer",
      "retry-after",
      "user-agent"
    ]);
    var parseHeaders = (rawHeaders) => {
      const parsed = {};
      let key;
      let val;
      let i;
      rawHeaders && rawHeaders.split("\n").forEach(function parser(line) {
        i = line.indexOf(":");
        key = line.substring(0, i).trim().toLowerCase();
        val = line.substring(i + 1).trim();
        if (!key || parsed[key] && ignoreDuplicateOf[key]) {
          return;
        }
        if (key === "set-cookie") {
          if (parsed[key]) {
            parsed[key].push(val);
          } else {
            parsed[key] = [val];
          }
        } else {
          parsed[key] = parsed[key] ? parsed[key] + ", " + val : val;
        }
      });
      return parsed;
    };
    var $internals = Symbol("internals");
    function normalizeHeader(header) {
      return header && String(header).trim().toLowerCase();
    }
    function normalizeValue(value) {
      if (value === false || value == null) {
        return value;
      }
      return utils$1.isArray(value) ? value.map(normalizeValue) : String(value);
    }
    function parseTokens(str) {
      const tokens = /* @__PURE__ */ Object.create(null);
      const tokensRE = /([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;
      let match;
      while (match = tokensRE.exec(str)) {
        tokens[match[1]] = match[2];
      }
      return tokens;
    }
    var isValidHeaderName = (str) => /^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(str.trim());
    function matchHeaderValue(context, value, header, filter, isHeaderNameFilter) {
      if (utils$1.isFunction(filter)) {
        return filter.call(this, value, header);
      }
      if (isHeaderNameFilter) {
        value = header;
      }
      if (!utils$1.isString(value)) return;
      if (utils$1.isString(filter)) {
        return value.indexOf(filter) !== -1;
      }
      if (utils$1.isRegExp(filter)) {
        return filter.test(value);
      }
    }
    function formatHeader(header) {
      return header.trim().toLowerCase().replace(/([a-z\d])(\w*)/g, (w, char, str) => {
        return char.toUpperCase() + str;
      });
    }
    function buildAccessors(obj, header) {
      const accessorName = utils$1.toCamelCase(" " + header);
      ["get", "set", "has"].forEach((methodName) => {
        Object.defineProperty(obj, methodName + accessorName, {
          value: function(arg1, arg2, arg3) {
            return this[methodName].call(this, header, arg1, arg2, arg3);
          },
          configurable: true
        });
      });
    }
    var AxiosHeaders = class {
      constructor(headers) {
        headers && this.set(headers);
      }
      set(header, valueOrRewrite, rewrite) {
        const self2 = this;
        function setHeader(_value, _header, _rewrite) {
          const lHeader = normalizeHeader(_header);
          if (!lHeader) {
            throw new Error("header name must be a non-empty string");
          }
          const key = utils$1.findKey(self2, lHeader);
          if (!key || self2[key] === void 0 || _rewrite === true || _rewrite === void 0 && self2[key] !== false) {
            self2[key || _header] = normalizeValue(_value);
          }
        }
        const setHeaders = (headers, _rewrite) => utils$1.forEach(headers, (_value, _header) => setHeader(_value, _header, _rewrite));
        if (utils$1.isPlainObject(header) || header instanceof this.constructor) {
          setHeaders(header, valueOrRewrite);
        } else if (utils$1.isString(header) && (header = header.trim()) && !isValidHeaderName(header)) {
          setHeaders(parseHeaders(header), valueOrRewrite);
        } else if (utils$1.isObject(header) && utils$1.isIterable(header)) {
          let obj = {}, dest, key;
          for (const entry of header) {
            if (!utils$1.isArray(entry)) {
              throw TypeError("Object iterator must return a key-value pair");
            }
            obj[key = entry[0]] = (dest = obj[key]) ? utils$1.isArray(dest) ? [...dest, entry[1]] : [dest, entry[1]] : entry[1];
          }
          setHeaders(obj, valueOrRewrite);
        } else {
          header != null && setHeader(valueOrRewrite, header, rewrite);
        }
        return this;
      }
      get(header, parser) {
        header = normalizeHeader(header);
        if (header) {
          const key = utils$1.findKey(this, header);
          if (key) {
            const value = this[key];
            if (!parser) {
              return value;
            }
            if (parser === true) {
              return parseTokens(value);
            }
            if (utils$1.isFunction(parser)) {
              return parser.call(this, value, key);
            }
            if (utils$1.isRegExp(parser)) {
              return parser.exec(value);
            }
            throw new TypeError("parser must be boolean|regexp|function");
          }
        }
      }
      has(header, matcher) {
        header = normalizeHeader(header);
        if (header) {
          const key = utils$1.findKey(this, header);
          return !!(key && this[key] !== void 0 && (!matcher || matchHeaderValue(this, this[key], key, matcher)));
        }
        return false;
      }
      delete(header, matcher) {
        const self2 = this;
        let deleted = false;
        function deleteHeader(_header) {
          _header = normalizeHeader(_header);
          if (_header) {
            const key = utils$1.findKey(self2, _header);
            if (key && (!matcher || matchHeaderValue(self2, self2[key], key, matcher))) {
              delete self2[key];
              deleted = true;
            }
          }
        }
        if (utils$1.isArray(header)) {
          header.forEach(deleteHeader);
        } else {
          deleteHeader(header);
        }
        return deleted;
      }
      clear(matcher) {
        const keys = Object.keys(this);
        let i = keys.length;
        let deleted = false;
        while (i--) {
          const key = keys[i];
          if (!matcher || matchHeaderValue(this, this[key], key, matcher, true)) {
            delete this[key];
            deleted = true;
          }
        }
        return deleted;
      }
      normalize(format) {
        const self2 = this;
        const headers = {};
        utils$1.forEach(this, (value, header) => {
          const key = utils$1.findKey(headers, header);
          if (key) {
            self2[key] = normalizeValue(value);
            delete self2[header];
            return;
          }
          const normalized = format ? formatHeader(header) : String(header).trim();
          if (normalized !== header) {
            delete self2[header];
          }
          self2[normalized] = normalizeValue(value);
          headers[normalized] = true;
        });
        return this;
      }
      concat(...targets) {
        return this.constructor.concat(this, ...targets);
      }
      toJSON(asStrings) {
        const obj = /* @__PURE__ */ Object.create(null);
        utils$1.forEach(this, (value, header) => {
          value != null && value !== false && (obj[header] = asStrings && utils$1.isArray(value) ? value.join(", ") : value);
        });
        return obj;
      }
      [Symbol.iterator]() {
        return Object.entries(this.toJSON())[Symbol.iterator]();
      }
      toString() {
        return Object.entries(this.toJSON()).map(([header, value]) => header + ": " + value).join("\n");
      }
      getSetCookie() {
        return this.get("set-cookie") || [];
      }
      get [Symbol.toStringTag]() {
        return "AxiosHeaders";
      }
      static from(thing) {
        return thing instanceof this ? thing : new this(thing);
      }
      static concat(first, ...targets) {
        const computed = new this(first);
        targets.forEach((target) => computed.set(target));
        return computed;
      }
      static accessor(header) {
        const internals = this[$internals] = this[$internals] = {
          accessors: {}
        };
        const accessors = internals.accessors;
        const prototype2 = this.prototype;
        function defineAccessor(_header) {
          const lHeader = normalizeHeader(_header);
          if (!accessors[lHeader]) {
            buildAccessors(prototype2, _header);
            accessors[lHeader] = true;
          }
        }
        utils$1.isArray(header) ? header.forEach(defineAccessor) : defineAccessor(header);
        return this;
      }
    };
    AxiosHeaders.accessor(["Content-Type", "Content-Length", "Accept", "Accept-Encoding", "User-Agent", "Authorization"]);
    utils$1.reduceDescriptors(AxiosHeaders.prototype, ({ value }, key) => {
      let mapped = key[0].toUpperCase() + key.slice(1);
      return {
        get: () => value,
        set(headerValue) {
          this[mapped] = headerValue;
        }
      };
    });
    utils$1.freezeMethods(AxiosHeaders);
    var AxiosHeaders$1 = AxiosHeaders;
    function transformData(fns, response) {
      const config = this || defaults$1;
      const context = response || config;
      const headers = AxiosHeaders$1.from(context.headers);
      let data = context.data;
      utils$1.forEach(fns, function transform(fn) {
        data = fn.call(config, data, headers.normalize(), response ? response.status : void 0);
      });
      headers.normalize();
      return data;
    }
    function isCancel(value) {
      return !!(value && value.__CANCEL__);
    }
    function CanceledError(message, config, request) {
      AxiosError.call(this, message == null ? "canceled" : message, AxiosError.ERR_CANCELED, config, request);
      this.name = "CanceledError";
    }
    utils$1.inherits(CanceledError, AxiosError, {
      __CANCEL__: true
    });
    function settle(resolve, reject, response) {
      const validateStatus = response.config.validateStatus;
      if (!response.status || !validateStatus || validateStatus(response.status)) {
        resolve(response);
      } else {
        reject(new AxiosError(
          "Request failed with status code " + response.status,
          [AxiosError.ERR_BAD_REQUEST, AxiosError.ERR_BAD_RESPONSE][Math.floor(response.status / 100) - 4],
          response.config,
          response.request,
          response
        ));
      }
    }
    function parseProtocol(url) {
      const match = /^([-+\w]{1,25})(:?\/\/|:)/.exec(url);
      return match && match[1] || "";
    }
    function speedometer(samplesCount, min) {
      samplesCount = samplesCount || 10;
      const bytes = new Array(samplesCount);
      const timestamps = new Array(samplesCount);
      let head = 0;
      let tail = 0;
      let firstSampleTS;
      min = min !== void 0 ? min : 1e3;
      return function push(chunkLength) {
        const now = Date.now();
        const startedAt = timestamps[tail];
        if (!firstSampleTS) {
          firstSampleTS = now;
        }
        bytes[head] = chunkLength;
        timestamps[head] = now;
        let i = tail;
        let bytesCount = 0;
        while (i !== head) {
          bytesCount += bytes[i++];
          i = i % samplesCount;
        }
        head = (head + 1) % samplesCount;
        if (head === tail) {
          tail = (tail + 1) % samplesCount;
        }
        if (now - firstSampleTS < min) {
          return;
        }
        const passed = startedAt && now - startedAt;
        return passed ? Math.round(bytesCount * 1e3 / passed) : void 0;
      };
    }
    function throttle(fn, freq) {
      let timestamp = 0;
      let threshold = 1e3 / freq;
      let lastArgs;
      let timer;
      const invoke = (args, now = Date.now()) => {
        timestamp = now;
        lastArgs = null;
        if (timer) {
          clearTimeout(timer);
          timer = null;
        }
        fn.apply(null, args);
      };
      const throttled = (...args) => {
        const now = Date.now();
        const passed = now - timestamp;
        if (passed >= threshold) {
          invoke(args, now);
        } else {
          lastArgs = args;
          if (!timer) {
            timer = setTimeout(() => {
              timer = null;
              invoke(lastArgs);
            }, threshold - passed);
          }
        }
      };
      const flush = () => lastArgs && invoke(lastArgs);
      return [throttled, flush];
    }
    var progressEventReducer = (listener, isDownloadStream, freq = 3) => {
      let bytesNotified = 0;
      const _speedometer = speedometer(50, 250);
      return throttle((e) => {
        const loaded = e.loaded;
        const total = e.lengthComputable ? e.total : void 0;
        const progressBytes = loaded - bytesNotified;
        const rate = _speedometer(progressBytes);
        const inRange = loaded <= total;
        bytesNotified = loaded;
        const data = {
          loaded,
          total,
          progress: total ? loaded / total : void 0,
          bytes: progressBytes,
          rate: rate ? rate : void 0,
          estimated: rate && total && inRange ? (total - loaded) / rate : void 0,
          event: e,
          lengthComputable: total != null,
          [isDownloadStream ? "download" : "upload"]: true
        };
        listener(data);
      }, freq);
    };
    var progressEventDecorator = (total, throttled) => {
      const lengthComputable = total != null;
      return [(loaded) => throttled[0]({
        lengthComputable,
        total,
        loaded
      }), throttled[1]];
    };
    var asyncDecorator = (fn) => (...args) => utils$1.asap(() => fn(...args));
    var isURLSameOrigin = platform.hasStandardBrowserEnv ? /* @__PURE__ */ ((origin2, isMSIE) => (url) => {
      url = new URL(url, platform.origin);
      return origin2.protocol === url.protocol && origin2.host === url.host && (isMSIE || origin2.port === url.port);
    })(
      new URL(platform.origin),
      platform.navigator && /(msie|trident)/i.test(platform.navigator.userAgent)
    ) : () => true;
    var cookies = platform.hasStandardBrowserEnv ? (
      // Standard browser envs support document.cookie
      {
        write(name, value, expires, path, domain, secure) {
          const cookie = [name + "=" + encodeURIComponent(value)];
          utils$1.isNumber(expires) && cookie.push("expires=" + new Date(expires).toGMTString());
          utils$1.isString(path) && cookie.push("path=" + path);
          utils$1.isString(domain) && cookie.push("domain=" + domain);
          secure === true && cookie.push("secure");
          document.cookie = cookie.join("; ");
        },
        read(name) {
          const match = document.cookie.match(new RegExp("(^|;\\s*)(" + name + ")=([^;]*)"));
          return match ? decodeURIComponent(match[3]) : null;
        },
        remove(name) {
          this.write(name, "", Date.now() - 864e5);
        }
      }
    ) : (
      // Non-standard browser env (web workers, react-native) lack needed support.
      {
        write() {
        },
        read() {
          return null;
        },
        remove() {
        }
      }
    );
    function isAbsoluteURL(url) {
      return /^([a-z][a-z\d+\-.]*:)?\/\//i.test(url);
    }
    function combineURLs(baseURL, relativeURL) {
      return relativeURL ? baseURL.replace(/\/?\/$/, "") + "/" + relativeURL.replace(/^\/+/, "") : baseURL;
    }
    function buildFullPath(baseURL, requestedURL, allowAbsoluteUrls) {
      let isRelativeUrl = !isAbsoluteURL(requestedURL);
      if (baseURL && (isRelativeUrl || allowAbsoluteUrls == false)) {
        return combineURLs(baseURL, requestedURL);
      }
      return requestedURL;
    }
    var headersToObject = (thing) => thing instanceof AxiosHeaders$1 ? { ...thing } : thing;
    function mergeConfig(config1, config2) {
      config2 = config2 || {};
      const config = {};
      function getMergedValue(target, source, prop, caseless) {
        if (utils$1.isPlainObject(target) && utils$1.isPlainObject(source)) {
          return utils$1.merge.call({ caseless }, target, source);
        } else if (utils$1.isPlainObject(source)) {
          return utils$1.merge({}, source);
        } else if (utils$1.isArray(source)) {
          return source.slice();
        }
        return source;
      }
      function mergeDeepProperties(a, b, prop, caseless) {
        if (!utils$1.isUndefined(b)) {
          return getMergedValue(a, b, prop, caseless);
        } else if (!utils$1.isUndefined(a)) {
          return getMergedValue(void 0, a, prop, caseless);
        }
      }
      function valueFromConfig2(a, b) {
        if (!utils$1.isUndefined(b)) {
          return getMergedValue(void 0, b);
        }
      }
      function defaultToConfig2(a, b) {
        if (!utils$1.isUndefined(b)) {
          return getMergedValue(void 0, b);
        } else if (!utils$1.isUndefined(a)) {
          return getMergedValue(void 0, a);
        }
      }
      function mergeDirectKeys(a, b, prop) {
        if (prop in config2) {
          return getMergedValue(a, b);
        } else if (prop in config1) {
          return getMergedValue(void 0, a);
        }
      }
      const mergeMap = {
        url: valueFromConfig2,
        method: valueFromConfig2,
        data: valueFromConfig2,
        baseURL: defaultToConfig2,
        transformRequest: defaultToConfig2,
        transformResponse: defaultToConfig2,
        paramsSerializer: defaultToConfig2,
        timeout: defaultToConfig2,
        timeoutMessage: defaultToConfig2,
        withCredentials: defaultToConfig2,
        withXSRFToken: defaultToConfig2,
        adapter: defaultToConfig2,
        responseType: defaultToConfig2,
        xsrfCookieName: defaultToConfig2,
        xsrfHeaderName: defaultToConfig2,
        onUploadProgress: defaultToConfig2,
        onDownloadProgress: defaultToConfig2,
        decompress: defaultToConfig2,
        maxContentLength: defaultToConfig2,
        maxBodyLength: defaultToConfig2,
        beforeRedirect: defaultToConfig2,
        transport: defaultToConfig2,
        httpAgent: defaultToConfig2,
        httpsAgent: defaultToConfig2,
        cancelToken: defaultToConfig2,
        socketPath: defaultToConfig2,
        responseEncoding: defaultToConfig2,
        validateStatus: mergeDirectKeys,
        headers: (a, b, prop) => mergeDeepProperties(headersToObject(a), headersToObject(b), prop, true)
      };
      utils$1.forEach(Object.keys(Object.assign({}, config1, config2)), function computeConfigValue(prop) {
        const merge2 = mergeMap[prop] || mergeDeepProperties;
        const configValue = merge2(config1[prop], config2[prop], prop);
        utils$1.isUndefined(configValue) && merge2 !== mergeDirectKeys || (config[prop] = configValue);
      });
      return config;
    }
    var resolveConfig = (config) => {
      const newConfig = mergeConfig({}, config);
      let { data, withXSRFToken, xsrfHeaderName, xsrfCookieName, headers, auth } = newConfig;
      newConfig.headers = headers = AxiosHeaders$1.from(headers);
      newConfig.url = buildURL(buildFullPath(newConfig.baseURL, newConfig.url, newConfig.allowAbsoluteUrls), config.params, config.paramsSerializer);
      if (auth) {
        headers.set(
          "Authorization",
          "Basic " + btoa((auth.username || "") + ":" + (auth.password ? unescape(encodeURIComponent(auth.password)) : ""))
        );
      }
      let contentType;
      if (utils$1.isFormData(data)) {
        if (platform.hasStandardBrowserEnv || platform.hasStandardBrowserWebWorkerEnv) {
          headers.setContentType(void 0);
        } else if ((contentType = headers.getContentType()) !== false) {
          const [type, ...tokens] = contentType ? contentType.split(";").map((token) => token.trim()).filter(Boolean) : [];
          headers.setContentType([type || "multipart/form-data", ...tokens].join("; "));
        }
      }
      if (platform.hasStandardBrowserEnv) {
        withXSRFToken && utils$1.isFunction(withXSRFToken) && (withXSRFToken = withXSRFToken(newConfig));
        if (withXSRFToken || withXSRFToken !== false && isURLSameOrigin(newConfig.url)) {
          const xsrfValue = xsrfHeaderName && xsrfCookieName && cookies.read(xsrfCookieName);
          if (xsrfValue) {
            headers.set(xsrfHeaderName, xsrfValue);
          }
        }
      }
      return newConfig;
    };
    var isXHRAdapterSupported = typeof XMLHttpRequest !== "undefined";
    var xhrAdapter = isXHRAdapterSupported && function(config) {
      return new Promise(function dispatchXhrRequest(resolve, reject) {
        const _config = resolveConfig(config);
        let requestData = _config.data;
        const requestHeaders = AxiosHeaders$1.from(_config.headers).normalize();
        let { responseType, onUploadProgress, onDownloadProgress } = _config;
        let onCanceled;
        let uploadThrottled, downloadThrottled;
        let flushUpload, flushDownload;
        function done() {
          flushUpload && flushUpload();
          flushDownload && flushDownload();
          _config.cancelToken && _config.cancelToken.unsubscribe(onCanceled);
          _config.signal && _config.signal.removeEventListener("abort", onCanceled);
        }
        let request = new XMLHttpRequest();
        request.open(_config.method.toUpperCase(), _config.url, true);
        request.timeout = _config.timeout;
        function onloadend() {
          if (!request) {
            return;
          }
          const responseHeaders = AxiosHeaders$1.from(
            "getAllResponseHeaders" in request && request.getAllResponseHeaders()
          );
          const responseData = !responseType || responseType === "text" || responseType === "json" ? request.responseText : request.response;
          const response = {
            data: responseData,
            status: request.status,
            statusText: request.statusText,
            headers: responseHeaders,
            config,
            request
          };
          settle(function _resolve(value) {
            resolve(value);
            done();
          }, function _reject(err) {
            reject(err);
            done();
          }, response);
          request = null;
        }
        if ("onloadend" in request) {
          request.onloadend = onloadend;
        } else {
          request.onreadystatechange = function handleLoad() {
            if (!request || request.readyState !== 4) {
              return;
            }
            if (request.status === 0 && !(request.responseURL && request.responseURL.indexOf("file:") === 0)) {
              return;
            }
            setTimeout(onloadend);
          };
        }
        request.onabort = function handleAbort() {
          if (!request) {
            return;
          }
          reject(new AxiosError("Request aborted", AxiosError.ECONNABORTED, config, request));
          request = null;
        };
        request.onerror = function handleError() {
          reject(new AxiosError("Network Error", AxiosError.ERR_NETWORK, config, request));
          request = null;
        };
        request.ontimeout = function handleTimeout() {
          let timeoutErrorMessage = _config.timeout ? "timeout of " + _config.timeout + "ms exceeded" : "timeout exceeded";
          const transitional = _config.transitional || transitionalDefaults;
          if (_config.timeoutErrorMessage) {
            timeoutErrorMessage = _config.timeoutErrorMessage;
          }
          reject(new AxiosError(
            timeoutErrorMessage,
            transitional.clarifyTimeoutError ? AxiosError.ETIMEDOUT : AxiosError.ECONNABORTED,
            config,
            request
          ));
          request = null;
        };
        requestData === void 0 && requestHeaders.setContentType(null);
        if ("setRequestHeader" in request) {
          utils$1.forEach(requestHeaders.toJSON(), function setRequestHeader(val, key) {
            request.setRequestHeader(key, val);
          });
        }
        if (!utils$1.isUndefined(_config.withCredentials)) {
          request.withCredentials = !!_config.withCredentials;
        }
        if (responseType && responseType !== "json") {
          request.responseType = _config.responseType;
        }
        if (onDownloadProgress) {
          [downloadThrottled, flushDownload] = progressEventReducer(onDownloadProgress, true);
          request.addEventListener("progress", downloadThrottled);
        }
        if (onUploadProgress && request.upload) {
          [uploadThrottled, flushUpload] = progressEventReducer(onUploadProgress);
          request.upload.addEventListener("progress", uploadThrottled);
          request.upload.addEventListener("loadend", flushUpload);
        }
        if (_config.cancelToken || _config.signal) {
          onCanceled = (cancel) => {
            if (!request) {
              return;
            }
            reject(!cancel || cancel.type ? new CanceledError(null, config, request) : cancel);
            request.abort();
            request = null;
          };
          _config.cancelToken && _config.cancelToken.subscribe(onCanceled);
          if (_config.signal) {
            _config.signal.aborted ? onCanceled() : _config.signal.addEventListener("abort", onCanceled);
          }
        }
        const protocol = parseProtocol(_config.url);
        if (protocol && platform.protocols.indexOf(protocol) === -1) {
          reject(new AxiosError("Unsupported protocol " + protocol + ":", AxiosError.ERR_BAD_REQUEST, config));
          return;
        }
        request.send(requestData || null);
      });
    };
    var composeSignals = (signals, timeout) => {
      const { length } = signals = signals ? signals.filter(Boolean) : [];
      if (timeout || length) {
        let controller = new AbortController();
        let aborted;
        const onabort = function(reason) {
          if (!aborted) {
            aborted = true;
            unsubscribe();
            const err = reason instanceof Error ? reason : this.reason;
            controller.abort(err instanceof AxiosError ? err : new CanceledError(err instanceof Error ? err.message : err));
          }
        };
        let timer = timeout && setTimeout(() => {
          timer = null;
          onabort(new AxiosError(`timeout ${timeout} of ms exceeded`, AxiosError.ETIMEDOUT));
        }, timeout);
        const unsubscribe = () => {
          if (signals) {
            timer && clearTimeout(timer);
            timer = null;
            signals.forEach((signal2) => {
              signal2.unsubscribe ? signal2.unsubscribe(onabort) : signal2.removeEventListener("abort", onabort);
            });
            signals = null;
          }
        };
        signals.forEach((signal2) => signal2.addEventListener("abort", onabort));
        const { signal } = controller;
        signal.unsubscribe = () => utils$1.asap(unsubscribe);
        return signal;
      }
    };
    var composeSignals$1 = composeSignals;
    var streamChunk = function* (chunk, chunkSize) {
      let len = chunk.byteLength;
      if (!chunkSize || len < chunkSize) {
        yield chunk;
        return;
      }
      let pos = 0;
      let end;
      while (pos < len) {
        end = pos + chunkSize;
        yield chunk.slice(pos, end);
        pos = end;
      }
    };
    var readBytes = async function* (iterable, chunkSize) {
      for await (const chunk of readStream(iterable)) {
        yield* streamChunk(chunk, chunkSize);
      }
    };
    var readStream = async function* (stream) {
      if (stream[Symbol.asyncIterator]) {
        yield* stream;
        return;
      }
      const reader = stream.getReader();
      try {
        for (; ; ) {
          const { done, value } = await reader.read();
          if (done) {
            break;
          }
          yield value;
        }
      } finally {
        await reader.cancel();
      }
    };
    var trackStream = (stream, chunkSize, onProgress, onFinish) => {
      const iterator2 = readBytes(stream, chunkSize);
      let bytes = 0;
      let done;
      let _onFinish = (e) => {
        if (!done) {
          done = true;
          onFinish && onFinish(e);
        }
      };
      return new ReadableStream({
        async pull(controller) {
          try {
            const { done: done2, value } = await iterator2.next();
            if (done2) {
              _onFinish();
              controller.close();
              return;
            }
            let len = value.byteLength;
            if (onProgress) {
              let loadedBytes = bytes += len;
              onProgress(loadedBytes);
            }
            controller.enqueue(new Uint8Array(value));
          } catch (err) {
            _onFinish(err);
            throw err;
          }
        },
        cancel(reason) {
          _onFinish(reason);
          return iterator2.return();
        }
      }, {
        highWaterMark: 2
      });
    };
    var isFetchSupported = typeof fetch === "function" && typeof Request === "function" && typeof Response === "function";
    var isReadableStreamSupported = isFetchSupported && typeof ReadableStream === "function";
    var encodeText = isFetchSupported && (typeof TextEncoder === "function" ? /* @__PURE__ */ ((encoder) => (str) => encoder.encode(str))(new TextEncoder()) : async (str) => new Uint8Array(await new Response(str).arrayBuffer()));
    var test = (fn, ...args) => {
      try {
        return !!fn(...args);
      } catch (e) {
        return false;
      }
    };
    var supportsRequestStream = isReadableStreamSupported && test(() => {
      let duplexAccessed = false;
      const hasContentType = new Request(platform.origin, {
        body: new ReadableStream(),
        method: "POST",
        get duplex() {
          duplexAccessed = true;
          return "half";
        }
      }).headers.has("Content-Type");
      return duplexAccessed && !hasContentType;
    });
    var DEFAULT_CHUNK_SIZE = 64 * 1024;
    var supportsResponseStream = isReadableStreamSupported && test(() => utils$1.isReadableStream(new Response("").body));
    var resolvers = {
      stream: supportsResponseStream && ((res) => res.body)
    };
    isFetchSupported && ((res) => {
      ["text", "arrayBuffer", "blob", "formData", "stream"].forEach((type) => {
        !resolvers[type] && (resolvers[type] = utils$1.isFunction(res[type]) ? (res2) => res2[type]() : (_, config) => {
          throw new AxiosError(`Response type '${type}' is not supported`, AxiosError.ERR_NOT_SUPPORT, config);
        });
      });
    })(new Response());
    var getBodyLength = async (body) => {
      if (body == null) {
        return 0;
      }
      if (utils$1.isBlob(body)) {
        return body.size;
      }
      if (utils$1.isSpecCompliantForm(body)) {
        const _request = new Request(platform.origin, {
          method: "POST",
          body
        });
        return (await _request.arrayBuffer()).byteLength;
      }
      if (utils$1.isArrayBufferView(body) || utils$1.isArrayBuffer(body)) {
        return body.byteLength;
      }
      if (utils$1.isURLSearchParams(body)) {
        body = body + "";
      }
      if (utils$1.isString(body)) {
        return (await encodeText(body)).byteLength;
      }
    };
    var resolveBodyLength = async (headers, body) => {
      const length = utils$1.toFiniteNumber(headers.getContentLength());
      return length == null ? getBodyLength(body) : length;
    };
    var fetchAdapter = isFetchSupported && (async (config) => {
      let {
        url,
        method,
        data,
        signal,
        cancelToken,
        timeout,
        onDownloadProgress,
        onUploadProgress,
        responseType,
        headers,
        withCredentials = "same-origin",
        fetchOptions
      } = resolveConfig(config);
      responseType = responseType ? (responseType + "").toLowerCase() : "text";
      let composedSignal = composeSignals$1([signal, cancelToken && cancelToken.toAbortSignal()], timeout);
      let request;
      const unsubscribe = composedSignal && composedSignal.unsubscribe && (() => {
        composedSignal.unsubscribe();
      });
      let requestContentLength;
      try {
        if (onUploadProgress && supportsRequestStream && method !== "get" && method !== "head" && (requestContentLength = await resolveBodyLength(headers, data)) !== 0) {
          let _request = new Request(url, {
            method: "POST",
            body: data,
            duplex: "half"
          });
          let contentTypeHeader;
          if (utils$1.isFormData(data) && (contentTypeHeader = _request.headers.get("content-type"))) {
            headers.setContentType(contentTypeHeader);
          }
          if (_request.body) {
            const [onProgress, flush] = progressEventDecorator(
              requestContentLength,
              progressEventReducer(asyncDecorator(onUploadProgress))
            );
            data = trackStream(_request.body, DEFAULT_CHUNK_SIZE, onProgress, flush);
          }
        }
        if (!utils$1.isString(withCredentials)) {
          withCredentials = withCredentials ? "include" : "omit";
        }
        const isCredentialsSupported = "credentials" in Request.prototype;
        request = new Request(url, {
          ...fetchOptions,
          signal: composedSignal,
          method: method.toUpperCase(),
          headers: headers.normalize().toJSON(),
          body: data,
          duplex: "half",
          credentials: isCredentialsSupported ? withCredentials : void 0
        });
        let response = await fetch(request);
        const isStreamResponse = supportsResponseStream && (responseType === "stream" || responseType === "response");
        if (supportsResponseStream && (onDownloadProgress || isStreamResponse && unsubscribe)) {
          const options = {};
          ["status", "statusText", "headers"].forEach((prop) => {
            options[prop] = response[prop];
          });
          const responseContentLength = utils$1.toFiniteNumber(response.headers.get("content-length"));
          const [onProgress, flush] = onDownloadProgress && progressEventDecorator(
            responseContentLength,
            progressEventReducer(asyncDecorator(onDownloadProgress), true)
          ) || [];
          response = new Response(
            trackStream(response.body, DEFAULT_CHUNK_SIZE, onProgress, () => {
              flush && flush();
              unsubscribe && unsubscribe();
            }),
            options
          );
        }
        responseType = responseType || "text";
        let responseData = await resolvers[utils$1.findKey(resolvers, responseType) || "text"](response, config);
        !isStreamResponse && unsubscribe && unsubscribe();
        return await new Promise((resolve, reject) => {
          settle(resolve, reject, {
            data: responseData,
            headers: AxiosHeaders$1.from(response.headers),
            status: response.status,
            statusText: response.statusText,
            config,
            request
          });
        });
      } catch (err) {
        unsubscribe && unsubscribe();
        if (err && err.name === "TypeError" && /Load failed|fetch/i.test(err.message)) {
          throw Object.assign(
            new AxiosError("Network Error", AxiosError.ERR_NETWORK, config, request),
            {
              cause: err.cause || err
            }
          );
        }
        throw AxiosError.from(err, err && err.code, config, request);
      }
    });
    var knownAdapters = {
      http: httpAdapter,
      xhr: xhrAdapter,
      fetch: fetchAdapter
    };
    utils$1.forEach(knownAdapters, (fn, value) => {
      if (fn) {
        try {
          Object.defineProperty(fn, "name", { value });
        } catch (e) {
        }
        Object.defineProperty(fn, "adapterName", { value });
      }
    });
    var renderReason = (reason) => `- ${reason}`;
    var isResolvedHandle = (adapter) => utils$1.isFunction(adapter) || adapter === null || adapter === false;
    var adapters = {
      getAdapter: (adapters2) => {
        adapters2 = utils$1.isArray(adapters2) ? adapters2 : [adapters2];
        const { length } = adapters2;
        let nameOrAdapter;
        let adapter;
        const rejectedReasons = {};
        for (let i = 0; i < length; i++) {
          nameOrAdapter = adapters2[i];
          let id;
          adapter = nameOrAdapter;
          if (!isResolvedHandle(nameOrAdapter)) {
            adapter = knownAdapters[(id = String(nameOrAdapter)).toLowerCase()];
            if (adapter === void 0) {
              throw new AxiosError(`Unknown adapter '${id}'`);
            }
          }
          if (adapter) {
            break;
          }
          rejectedReasons[id || "#" + i] = adapter;
        }
        if (!adapter) {
          const reasons = Object.entries(rejectedReasons).map(
            ([id, state]) => `adapter ${id} ` + (state === false ? "is not supported by the environment" : "is not available in the build")
          );
          let s = length ? reasons.length > 1 ? "since :\n" + reasons.map(renderReason).join("\n") : " " + renderReason(reasons[0]) : "as no adapter specified";
          throw new AxiosError(
            `There is no suitable adapter to dispatch the request ` + s,
            "ERR_NOT_SUPPORT"
          );
        }
        return adapter;
      },
      adapters: knownAdapters
    };
    function throwIfCancellationRequested(config) {
      if (config.cancelToken) {
        config.cancelToken.throwIfRequested();
      }
      if (config.signal && config.signal.aborted) {
        throw new CanceledError(null, config);
      }
    }
    function dispatchRequest(config) {
      throwIfCancellationRequested(config);
      config.headers = AxiosHeaders$1.from(config.headers);
      config.data = transformData.call(
        config,
        config.transformRequest
      );
      if (["post", "put", "patch"].indexOf(config.method) !== -1) {
        config.headers.setContentType("application/x-www-form-urlencoded", false);
      }
      const adapter = adapters.getAdapter(config.adapter || defaults$1.adapter);
      return adapter(config).then(function onAdapterResolution(response) {
        throwIfCancellationRequested(config);
        response.data = transformData.call(
          config,
          config.transformResponse,
          response
        );
        response.headers = AxiosHeaders$1.from(response.headers);
        return response;
      }, function onAdapterRejection(reason) {
        if (!isCancel(reason)) {
          throwIfCancellationRequested(config);
          if (reason && reason.response) {
            reason.response.data = transformData.call(
              config,
              config.transformResponse,
              reason.response
            );
            reason.response.headers = AxiosHeaders$1.from(reason.response.headers);
          }
        }
        return Promise.reject(reason);
      });
    }
    var VERSION = "1.9.0";
    var validators$1 = {};
    ["object", "boolean", "number", "function", "string", "symbol"].forEach((type, i) => {
      validators$1[type] = function validator2(thing) {
        return typeof thing === type || "a" + (i < 1 ? "n " : " ") + type;
      };
    });
    var deprecatedWarnings = {};
    validators$1.transitional = function transitional(validator2, version, message) {
      function formatMessage(opt, desc) {
        return "[Axios v" + VERSION + "] Transitional option '" + opt + "'" + desc + (message ? ". " + message : "");
      }
      return (value, opt, opts) => {
        if (validator2 === false) {
          throw new AxiosError(
            formatMessage(opt, " has been removed" + (version ? " in " + version : "")),
            AxiosError.ERR_DEPRECATED
          );
        }
        if (version && !deprecatedWarnings[opt]) {
          deprecatedWarnings[opt] = true;
          console.warn(
            formatMessage(
              opt,
              " has been deprecated since v" + version + " and will be removed in the near future"
            )
          );
        }
        return validator2 ? validator2(value, opt, opts) : true;
      };
    };
    validators$1.spelling = function spelling(correctSpelling) {
      return (value, opt) => {
        console.warn(`${opt} is likely a misspelling of ${correctSpelling}`);
        return true;
      };
    };
    function assertOptions(options, schema, allowUnknown) {
      if (typeof options !== "object") {
        throw new AxiosError("options must be an object", AxiosError.ERR_BAD_OPTION_VALUE);
      }
      const keys = Object.keys(options);
      let i = keys.length;
      while (i-- > 0) {
        const opt = keys[i];
        const validator2 = schema[opt];
        if (validator2) {
          const value = options[opt];
          const result = value === void 0 || validator2(value, opt, options);
          if (result !== true) {
            throw new AxiosError("option " + opt + " must be " + result, AxiosError.ERR_BAD_OPTION_VALUE);
          }
          continue;
        }
        if (allowUnknown !== true) {
          throw new AxiosError("Unknown option " + opt, AxiosError.ERR_BAD_OPTION);
        }
      }
    }
    var validator = {
      assertOptions,
      validators: validators$1
    };
    var validators = validator.validators;
    var Axios = class {
      constructor(instanceConfig) {
        this.defaults = instanceConfig || {};
        this.interceptors = {
          request: new InterceptorManager$1(),
          response: new InterceptorManager$1()
        };
      }
      /**
       * Dispatch a request
       *
       * @param {String|Object} configOrUrl The config specific for this request (merged with this.defaults)
       * @param {?Object} config
       *
       * @returns {Promise} The Promise to be fulfilled
       */
      async request(configOrUrl, config) {
        try {
          return await this._request(configOrUrl, config);
        } catch (err) {
          if (err instanceof Error) {
            let dummy = {};
            Error.captureStackTrace ? Error.captureStackTrace(dummy) : dummy = new Error();
            const stack = dummy.stack ? dummy.stack.replace(/^.+\n/, "") : "";
            try {
              if (!err.stack) {
                err.stack = stack;
              } else if (stack && !String(err.stack).endsWith(stack.replace(/^.+\n.+\n/, ""))) {
                err.stack += "\n" + stack;
              }
            } catch (e) {
            }
          }
          throw err;
        }
      }
      _request(configOrUrl, config) {
        if (typeof configOrUrl === "string") {
          config = config || {};
          config.url = configOrUrl;
        } else {
          config = configOrUrl || {};
        }
        config = mergeConfig(this.defaults, config);
        const { transitional, paramsSerializer, headers } = config;
        if (transitional !== void 0) {
          validator.assertOptions(transitional, {
            silentJSONParsing: validators.transitional(validators.boolean),
            forcedJSONParsing: validators.transitional(validators.boolean),
            clarifyTimeoutError: validators.transitional(validators.boolean)
          }, false);
        }
        if (paramsSerializer != null) {
          if (utils$1.isFunction(paramsSerializer)) {
            config.paramsSerializer = {
              serialize: paramsSerializer
            };
          } else {
            validator.assertOptions(paramsSerializer, {
              encode: validators.function,
              serialize: validators.function
            }, true);
          }
        }
        if (config.allowAbsoluteUrls !== void 0) ;
        else if (this.defaults.allowAbsoluteUrls !== void 0) {
          config.allowAbsoluteUrls = this.defaults.allowAbsoluteUrls;
        } else {
          config.allowAbsoluteUrls = true;
        }
        validator.assertOptions(config, {
          baseUrl: validators.spelling("baseURL"),
          withXsrfToken: validators.spelling("withXSRFToken")
        }, true);
        config.method = (config.method || this.defaults.method || "get").toLowerCase();
        let contextHeaders = headers && utils$1.merge(
          headers.common,
          headers[config.method]
        );
        headers && utils$1.forEach(
          ["delete", "get", "head", "post", "put", "patch", "common"],
          (method) => {
            delete headers[method];
          }
        );
        config.headers = AxiosHeaders$1.concat(contextHeaders, headers);
        const requestInterceptorChain = [];
        let synchronousRequestInterceptors = true;
        this.interceptors.request.forEach(function unshiftRequestInterceptors(interceptor) {
          if (typeof interceptor.runWhen === "function" && interceptor.runWhen(config) === false) {
            return;
          }
          synchronousRequestInterceptors = synchronousRequestInterceptors && interceptor.synchronous;
          requestInterceptorChain.unshift(interceptor.fulfilled, interceptor.rejected);
        });
        const responseInterceptorChain = [];
        this.interceptors.response.forEach(function pushResponseInterceptors(interceptor) {
          responseInterceptorChain.push(interceptor.fulfilled, interceptor.rejected);
        });
        let promise;
        let i = 0;
        let len;
        if (!synchronousRequestInterceptors) {
          const chain = [dispatchRequest.bind(this), void 0];
          chain.unshift.apply(chain, requestInterceptorChain);
          chain.push.apply(chain, responseInterceptorChain);
          len = chain.length;
          promise = Promise.resolve(config);
          while (i < len) {
            promise = promise.then(chain[i++], chain[i++]);
          }
          return promise;
        }
        len = requestInterceptorChain.length;
        let newConfig = config;
        i = 0;
        while (i < len) {
          const onFulfilled = requestInterceptorChain[i++];
          const onRejected = requestInterceptorChain[i++];
          try {
            newConfig = onFulfilled(newConfig);
          } catch (error) {
            onRejected.call(this, error);
            break;
          }
        }
        try {
          promise = dispatchRequest.call(this, newConfig);
        } catch (error) {
          return Promise.reject(error);
        }
        i = 0;
        len = responseInterceptorChain.length;
        while (i < len) {
          promise = promise.then(responseInterceptorChain[i++], responseInterceptorChain[i++]);
        }
        return promise;
      }
      getUri(config) {
        config = mergeConfig(this.defaults, config);
        const fullPath = buildFullPath(config.baseURL, config.url, config.allowAbsoluteUrls);
        return buildURL(fullPath, config.params, config.paramsSerializer);
      }
    };
    utils$1.forEach(["delete", "get", "head", "options"], function forEachMethodNoData(method) {
      Axios.prototype[method] = function(url, config) {
        return this.request(mergeConfig(config || {}, {
          method,
          url,
          data: (config || {}).data
        }));
      };
    });
    utils$1.forEach(["post", "put", "patch"], function forEachMethodWithData(method) {
      function generateHTTPMethod(isForm) {
        return function httpMethod(url, data, config) {
          return this.request(mergeConfig(config || {}, {
            method,
            headers: isForm ? {
              "Content-Type": "multipart/form-data"
            } : {},
            url,
            data
          }));
        };
      }
      Axios.prototype[method] = generateHTTPMethod();
      Axios.prototype[method + "Form"] = generateHTTPMethod(true);
    });
    var Axios$1 = Axios;
    var CancelToken = class _CancelToken {
      constructor(executor) {
        if (typeof executor !== "function") {
          throw new TypeError("executor must be a function.");
        }
        let resolvePromise;
        this.promise = new Promise(function promiseExecutor(resolve) {
          resolvePromise = resolve;
        });
        const token = this;
        this.promise.then((cancel) => {
          if (!token._listeners) return;
          let i = token._listeners.length;
          while (i-- > 0) {
            token._listeners[i](cancel);
          }
          token._listeners = null;
        });
        this.promise.then = (onfulfilled) => {
          let _resolve;
          const promise = new Promise((resolve) => {
            token.subscribe(resolve);
            _resolve = resolve;
          }).then(onfulfilled);
          promise.cancel = function reject() {
            token.unsubscribe(_resolve);
          };
          return promise;
        };
        executor(function cancel(message, config, request) {
          if (token.reason) {
            return;
          }
          token.reason = new CanceledError(message, config, request);
          resolvePromise(token.reason);
        });
      }
      /**
       * Throws a `CanceledError` if cancellation has been requested.
       */
      throwIfRequested() {
        if (this.reason) {
          throw this.reason;
        }
      }
      /**
       * Subscribe to the cancel signal
       */
      subscribe(listener) {
        if (this.reason) {
          listener(this.reason);
          return;
        }
        if (this._listeners) {
          this._listeners.push(listener);
        } else {
          this._listeners = [listener];
        }
      }
      /**
       * Unsubscribe from the cancel signal
       */
      unsubscribe(listener) {
        if (!this._listeners) {
          return;
        }
        const index = this._listeners.indexOf(listener);
        if (index !== -1) {
          this._listeners.splice(index, 1);
        }
      }
      toAbortSignal() {
        const controller = new AbortController();
        const abort = (err) => {
          controller.abort(err);
        };
        this.subscribe(abort);
        controller.signal.unsubscribe = () => this.unsubscribe(abort);
        return controller.signal;
      }
      /**
       * Returns an object that contains a new `CancelToken` and a function that, when called,
       * cancels the `CancelToken`.
       */
      static source() {
        let cancel;
        const token = new _CancelToken(function executor(c) {
          cancel = c;
        });
        return {
          token,
          cancel
        };
      }
    };
    var CancelToken$1 = CancelToken;
    function spread(callback) {
      return function wrap(arr) {
        return callback.apply(null, arr);
      };
    }
    function isAxiosError(payload) {
      return utils$1.isObject(payload) && payload.isAxiosError === true;
    }
    var HttpStatusCode = {
      Continue: 100,
      SwitchingProtocols: 101,
      Processing: 102,
      EarlyHints: 103,
      Ok: 200,
      Created: 201,
      Accepted: 202,
      NonAuthoritativeInformation: 203,
      NoContent: 204,
      ResetContent: 205,
      PartialContent: 206,
      MultiStatus: 207,
      AlreadyReported: 208,
      ImUsed: 226,
      MultipleChoices: 300,
      MovedPermanently: 301,
      Found: 302,
      SeeOther: 303,
      NotModified: 304,
      UseProxy: 305,
      Unused: 306,
      TemporaryRedirect: 307,
      PermanentRedirect: 308,
      BadRequest: 400,
      Unauthorized: 401,
      PaymentRequired: 402,
      Forbidden: 403,
      NotFound: 404,
      MethodNotAllowed: 405,
      NotAcceptable: 406,
      ProxyAuthenticationRequired: 407,
      RequestTimeout: 408,
      Conflict: 409,
      Gone: 410,
      LengthRequired: 411,
      PreconditionFailed: 412,
      PayloadTooLarge: 413,
      UriTooLong: 414,
      UnsupportedMediaType: 415,
      RangeNotSatisfiable: 416,
      ExpectationFailed: 417,
      ImATeapot: 418,
      MisdirectedRequest: 421,
      UnprocessableEntity: 422,
      Locked: 423,
      FailedDependency: 424,
      TooEarly: 425,
      UpgradeRequired: 426,
      PreconditionRequired: 428,
      TooManyRequests: 429,
      RequestHeaderFieldsTooLarge: 431,
      UnavailableForLegalReasons: 451,
      InternalServerError: 500,
      NotImplemented: 501,
      BadGateway: 502,
      ServiceUnavailable: 503,
      GatewayTimeout: 504,
      HttpVersionNotSupported: 505,
      VariantAlsoNegotiates: 506,
      InsufficientStorage: 507,
      LoopDetected: 508,
      NotExtended: 510,
      NetworkAuthenticationRequired: 511
    };
    Object.entries(HttpStatusCode).forEach(([key, value]) => {
      HttpStatusCode[value] = key;
    });
    var HttpStatusCode$1 = HttpStatusCode;
    function createInstance(defaultConfig) {
      const context = new Axios$1(defaultConfig);
      const instance = bind(Axios$1.prototype.request, context);
      utils$1.extend(instance, Axios$1.prototype, context, { allOwnKeys: true });
      utils$1.extend(instance, context, null, { allOwnKeys: true });
      instance.create = function create(instanceConfig) {
        return createInstance(mergeConfig(defaultConfig, instanceConfig));
      };
      return instance;
    }
    var axios = createInstance(defaults$1);
    axios.Axios = Axios$1;
    axios.CanceledError = CanceledError;
    axios.CancelToken = CancelToken$1;
    axios.isCancel = isCancel;
    axios.VERSION = VERSION;
    axios.toFormData = toFormData;
    axios.AxiosError = AxiosError;
    axios.Cancel = axios.CanceledError;
    axios.all = function all(promises) {
      return Promise.all(promises);
    };
    axios.spread = spread;
    axios.isAxiosError = isAxiosError;
    axios.mergeConfig = mergeConfig;
    axios.AxiosHeaders = AxiosHeaders$1;
    axios.formToJSON = (thing) => formDataToJSON(utils$1.isHTMLForm(thing) ? new FormData(thing) : thing);
    axios.getAdapter = adapters.getAdapter;
    axios.HttpStatusCode = HttpStatusCode$1;
    axios.default = axios;
    module.exports = axios;
  }
});

// node_modules/razorpay/dist/utils/nodeify.js
var require_nodeify = __commonJS({
  "node_modules/razorpay/dist/utils/nodeify.js"(exports, module) {
    "use strict";
    var nodeify = function nodeify2(promise, cb) {
      if (!cb) {
        return promise.then(function(response) {
          return response.data;
        });
      }
      return promise.then(function(response) {
        cb(null, response.data);
      }).catch(function(error) {
        cb(error, null);
      });
    };
    module.exports = nodeify;
  }
});

// browser-external:crypto
var require_crypto = __commonJS({
  "browser-external:crypto"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "crypto" has been externalized for browser compatibility. Cannot access "crypto.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// node_modules/razorpay/dist/utils/razorpay-utils.js
var require_razorpay_utils = __commonJS({
  "node_modules/razorpay/dist/utils/razorpay-utils.js"(exports, module) {
    "use strict";
    var _typeof = typeof Symbol === "function" && typeof Symbol.iterator === "symbol" ? function(obj) {
      return typeof obj;
    } : function(obj) {
      return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj;
    };
    var crypto = require_crypto();
    function getDateInSecs(date) {
      return +new Date(date) / 1e3;
    }
    function normalizeDate(date) {
      return isNumber(date) ? date : getDateInSecs(date);
    }
    function isNumber(num) {
      return !isNaN(Number(num));
    }
    function isNonNullObject(input) {
      return !!input && (typeof input === "undefined" ? "undefined" : _typeof(input)) === "object" && !Array.isArray(input);
    }
    function normalizeBoolean(bool) {
      if (bool === void 0) {
        return bool;
      }
      return bool ? 1 : 0;
    }
    function isDefined(value) {
      return typeof value !== "undefined";
    }
    function normalizeNotes() {
      var notes = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
      var normalizedNotes = {};
      for (var key in notes) {
        normalizedNotes["notes[" + key + "]"] = notes[key];
      }
      return normalizedNotes;
    }
    function prettify(val) {
      return JSON.stringify(val, null, 2);
    }
    function getTestError(summary, expectedVal, gotVal) {
      return new Error("\n" + summary + "\n" + ("Expected(" + (typeof expectedVal === "undefined" ? "undefined" : _typeof(expectedVal)) + ")\n" + prettify(expectedVal) + "\n\n") + ("Got(" + (typeof gotVal === "undefined" ? "undefined" : _typeof(gotVal)) + ")\n" + prettify(gotVal)));
    }
    function validateWebhookSignature(body, signature, secret) {
      var crypto2 = require_crypto();
      if (!isDefined(body) || !isDefined(signature) || !isDefined(secret)) {
        throw Error("Invalid Parameters: Please give request body,signature sent in X-Razorpay-Signature header and webhook secret from dashboard as parameters");
      }
      body = body.toString();
      var expectedSignature = crypto2.createHmac("sha256", secret).update(body).digest("hex");
      return expectedSignature === signature;
    }
    function validatePaymentVerification() {
      var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
      var signature = arguments[1];
      var secret = arguments[2];
      var paymentId = params.payment_id;
      if (!secret) {
        throw new Error("secret is mandatory");
      }
      if (isDefined(params.order_id) === true) {
        var orderId = params.order_id;
        var payload = orderId + "|" + paymentId;
      } else if (isDefined(params.subscription_id) === true) {
        var subscriptionId = params.subscription_id;
        var payload = paymentId + "|" + subscriptionId;
      } else if (isDefined(params.payment_link_id) === true) {
        var paymentLinkId = params.payment_link_id;
        var paymentLinkRefId = params.payment_link_reference_id;
        var paymentLinkStatus = params.payment_link_status;
        var payload = paymentLinkId + "|" + paymentLinkRefId + "|" + paymentLinkStatus + "|" + paymentId;
      } else {
        throw new Error("Either order_id or subscription_id is mandatory");
      }
      return validateWebhookSignature(payload, signature, secret);
    }
    function generateOnboardingSignature() {
      var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
      var secret = arguments[1];
      var jsonStr = JSON.stringify(params);
      return encrypt(jsonStr, secret);
    }
    function encrypt(dataToEncrypt, secret) {
      try {
        var keyBytes = Buffer.from(secret.slice(0, 16), "utf8");
        var iv = Buffer.alloc(12);
        keyBytes.copy(iv, 0, 0, 12);
        var cipher = crypto.createCipheriv("aes-128-gcm", keyBytes, iv);
        var encryptedData = cipher.update(dataToEncrypt, "utf8");
        encryptedData = Buffer.concat([encryptedData, cipher.final()]);
        var authTag = cipher.getAuthTag();
        var finalData = Buffer.concat([encryptedData, authTag]);
        return finalData.toString("hex");
      } catch (err) {
        throw new Error("Encryption failed: " + err.message);
      }
    }
    function isValidUrl(url) {
      try {
        new URL(url);
        return true;
      } catch (error) {
        return false;
      }
    }
    module.exports = {
      normalizeNotes,
      normalizeDate,
      normalizeBoolean,
      isNumber,
      getDateInSecs,
      prettify,
      isDefined,
      isNonNullObject,
      getTestError,
      validateWebhookSignature,
      validatePaymentVerification,
      isValidUrl,
      generateOnboardingSignature
    };
  }
});

// node_modules/razorpay/dist/api.js
var require_api = __commonJS({
  "node_modules/razorpay/dist/api.js"(exports, module) {
    "use strict";
    var _extends = Object.assign || function(target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i];
        for (var key in source) {
          if (Object.prototype.hasOwnProperty.call(source, key)) {
            target[key] = source[key];
          }
        }
      }
      return target;
    };
    var _createClass = /* @__PURE__ */ function() {
      function defineProperties(target, props) {
        for (var i = 0; i < props.length; i++) {
          var descriptor = props[i];
          descriptor.enumerable = descriptor.enumerable || false;
          descriptor.configurable = true;
          if ("value" in descriptor) descriptor.writable = true;
          Object.defineProperty(target, descriptor.key, descriptor);
        }
      }
      return function(Constructor, protoProps, staticProps) {
        if (protoProps) defineProperties(Constructor.prototype, protoProps);
        if (staticProps) defineProperties(Constructor, staticProps);
        return Constructor;
      };
    }();
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    var axios = require_axios().default;
    var nodeify = require_nodeify();
    var _require = require_razorpay_utils();
    var isNonNullObject = _require.isNonNullObject;
    var allowedHeaders = {
      "X-Razorpay-Account": "",
      "Content-Type": "application/json"
    };
    function getValidHeaders(headers) {
      var result = {};
      if (!isNonNullObject(headers)) {
        return result;
      }
      return Object.keys(headers).reduce(function(result2, headerName) {
        if (allowedHeaders.hasOwnProperty(headerName)) {
          result2[headerName] = headers[headerName];
        }
        return result2;
      }, result);
    }
    function normalizeError(err) {
      throw {
        statusCode: err.response.status,
        error: err.response.data.error
      };
    }
    var API = function() {
      function API2(options) {
        _classCallCheck(this, API2);
        this.version = "v1";
        this.rq = axios.create(this._createConfig(options));
      }
      _createClass(API2, [{
        key: "_createConfig",
        value: function _createConfig(options) {
          var config = {
            baseURL: options.hostUrl,
            headers: Object.assign({ "User-Agent": options.ua }, getValidHeaders(options.headers))
          };
          if (options.key_id && options.key_secret) {
            config.auth = {
              username: options.key_id,
              password: options.key_secret
            };
          }
          if (options.oauthToken) {
            config.headers = _extends({
              "Authorization": "Bearer " + options.oauthToken
            }, config.headers);
          }
          return config;
        }
      }, {
        key: "getEntityUrl",
        value: function getEntityUrl(params) {
          return params.hasOwnProperty("version") ? "/" + params.version + params.url : "/" + this.version + params.url;
        }
      }, {
        key: "get",
        value: function get(params, cb) {
          return nodeify(this.rq.get(this.getEntityUrl(params), {
            params: params.data
          }).catch(normalizeError), cb);
        }
      }, {
        key: "post",
        value: function post(params, cb) {
          return nodeify(this.rq.post(this.getEntityUrl(params), params.data).catch(normalizeError), cb);
        }
        // postFormData method for file uploads.
      }, {
        key: "postFormData",
        value: function postFormData(params, cb) {
          return nodeify(this.rq.post(this.getEntityUrl(params), params.formData, {
            "headers": {
              "Content-Type": "multipart/form-data"
            }
          }).catch(normalizeError), cb);
        }
      }, {
        key: "put",
        value: function put(params, cb) {
          return nodeify(this.rq.put(this.getEntityUrl(params), params.data).catch(normalizeError), cb);
        }
      }, {
        key: "patch",
        value: function patch(params, cb) {
          return nodeify(this.rq.patch(this.getEntityUrl(params), params.data).catch(normalizeError), cb);
        }
      }, {
        key: "delete",
        value: function _delete(params, cb) {
          return nodeify(this.rq.delete(this.getEntityUrl(params)).catch(normalizeError), cb);
        }
      }]);
      return API2;
    }();
    module.exports = API;
  }
});

// node_modules/razorpay/package.json
var require_package = __commonJS({
  "node_modules/razorpay/package.json"(exports, module) {
    module.exports = {
      name: "razorpay",
      version: "2.9.6",
      description: "Official Node SDK for Razorpay API",
      main: "dist/razorpay",
      typings: "dist/razorpay",
      scripts: {
        prepublish: "npm test",
        clean: "rm -rf dist && mkdir dist",
        "cp-types": "mkdir dist/types && cp lib/types/* dist/types && cp lib/utils/*d.ts dist/utils",
        "cp-ts": "cp lib/razorpay.d.ts dist/ && cp lib/oAuthTokenClient.d.ts dist/ && npm run cp-types",
        "build:commonjs": "babel lib -d dist",
        build: "npm run clean && npm run build:commonjs && npm run cp-ts",
        debug: "npm run build && node-debug examples/index.js",
        test: "npm run build && mocha --recursive --require babel-register test/ && nyc --reporter=text mocha",
        coverage: "nyc report --reporter=text-lcov > coverage.lcov"
      },
      repository: {
        type: "git",
        url: "https://github.com/razorpay/razorpay-node.git"
      },
      keywords: [
        "razorpay",
        "payments",
        "node",
        "nodejs",
        "razorpay-node"
      ],
      files: [
        "dist"
      ],
      mocha: {
        recursive: true,
        "full-trace": true
      },
      license: "MIT",
      devDependencies: {
        "@types/node": "^20.12.12",
        "babel-cli": "^6.26.0",
        "babel-preset-env": "^1.7.0",
        "babel-preset-stage-0": "^6.24.0",
        "babel-register": "^6.26.0",
        chai: "^4.3.4",
        "deep-equal": "^2.0.5",
        mocha: "^9.0.0",
        nock: "^13.1.1",
        nyc: "^15.1.0",
        typescript: "^4.9.4"
      },
      dependencies: {
        axios: "^1.6.8"
      }
    };
  }
});

// node_modules/razorpay/dist/resources/accounts.js
var require_accounts = __commonJS({
  "node_modules/razorpay/dist/resources/accounts.js"(exports, module) {
    "use strict";
    var _extends = Object.assign || function(target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i];
        for (var key in source) {
          if (Object.prototype.hasOwnProperty.call(source, key)) {
            target[key] = source[key];
          }
        }
      }
      return target;
    };
    function _objectWithoutProperties(obj, keys) {
      var target = {};
      for (var i in obj) {
        if (keys.indexOf(i) >= 0) continue;
        if (!Object.prototype.hasOwnProperty.call(obj, i)) continue;
        target[i] = obj[i];
      }
      return target;
    }
    module.exports = function(api) {
      var BASE_URL = "/accounts";
      return {
        create: function create(params, callback) {
          return api.post({
            version: "v2",
            url: "" + BASE_URL,
            data: params
          }, callback);
        },
        edit: function edit(accountId, params, callback) {
          return api.patch({
            version: "v2",
            url: BASE_URL + "/" + accountId,
            data: params
          }, callback);
        },
        fetch: function fetch2(accountId, callback) {
          return api.get({
            version: "v2",
            url: BASE_URL + "/" + accountId
          }, callback);
        },
        delete: function _delete(accountId, callback) {
          return api.delete({
            version: "v2",
            url: BASE_URL + "/" + accountId
          }, callback);
        },
        uploadAccountDoc: function uploadAccountDoc(accountId, params, callback) {
          var file = params.file, rest = _objectWithoutProperties(params, ["file"]);
          return api.postFormData({
            version: "v2",
            url: BASE_URL + "/" + accountId + "/documents",
            formData: _extends({
              file: file.value
            }, rest)
          }, callback);
        },
        fetchAccountDoc: function fetchAccountDoc(accountId, callback) {
          return api.get({
            version: "v2",
            url: BASE_URL + "/" + accountId + "/documents"
          }, callback);
        }
      };
    };
  }
});

// node_modules/razorpay/dist/resources/stakeholders.js
var require_stakeholders = __commonJS({
  "node_modules/razorpay/dist/resources/stakeholders.js"(exports, module) {
    "use strict";
    var _extends = Object.assign || function(target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i];
        for (var key in source) {
          if (Object.prototype.hasOwnProperty.call(source, key)) {
            target[key] = source[key];
          }
        }
      }
      return target;
    };
    function _objectWithoutProperties(obj, keys) {
      var target = {};
      for (var i in obj) {
        if (keys.indexOf(i) >= 0) continue;
        if (!Object.prototype.hasOwnProperty.call(obj, i)) continue;
        target[i] = obj[i];
      }
      return target;
    }
    module.exports = function(api) {
      var BASE_URL = "/accounts";
      return {
        create: function create(accountId, params, callback) {
          return api.post({
            version: "v2",
            url: BASE_URL + "/" + accountId + "/stakeholders",
            data: params
          }, callback);
        },
        edit: function edit(accountId, stakeholderId, params, callback) {
          return api.patch({
            version: "v2",
            url: BASE_URL + "/" + accountId + "/stakeholders/" + stakeholderId,
            data: params
          }, callback);
        },
        fetch: function fetch2(accountId, stakeholderId, callback) {
          return api.get({
            version: "v2",
            url: BASE_URL + "/" + accountId + "/stakeholders/" + stakeholderId
          }, callback);
        },
        all: function all(accountId, callback) {
          return api.get({
            version: "v2",
            url: BASE_URL + "/" + accountId + "/stakeholders"
          }, callback);
        },
        uploadStakeholderDoc: function uploadStakeholderDoc(accountId, stakeholderId, params, callback) {
          var file = params.file, rest = _objectWithoutProperties(params, ["file"]);
          return api.postFormData({
            version: "v2",
            url: BASE_URL + "/" + accountId + "/stakeholders/" + stakeholderId + "/documents",
            formData: _extends({
              file: file.value
            }, rest)
          }, callback);
        },
        fetchStakeholderDoc: function fetchStakeholderDoc(accountId, stakeholderId, callback) {
          return api.get({
            version: "v2",
            url: BASE_URL + "/" + accountId + "/stakeholders/" + stakeholderId + "/documents"
          }, callback);
        }
      };
    };
  }
});

// node_modules/razorpay/dist/resources/payments.js
var require_payments = __commonJS({
  "node_modules/razorpay/dist/resources/payments.js"(exports, module) {
    "use strict";
    var _extends = Object.assign || function(target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i];
        for (var key in source) {
          if (Object.prototype.hasOwnProperty.call(source, key)) {
            target[key] = source[key];
          }
        }
      }
      return target;
    };
    function _objectWithoutProperties(obj, keys) {
      var target = {};
      for (var i in obj) {
        if (keys.indexOf(i) >= 0) continue;
        if (!Object.prototype.hasOwnProperty.call(obj, i)) continue;
        target[i] = obj[i];
      }
      return target;
    }
    var _require = require_razorpay_utils();
    var normalizeDate = _require.normalizeDate;
    var ID_REQUIRED_MSG = "`payment_id` is mandatory";
    var BASE_URL = "/payments";
    module.exports = function(api) {
      return {
        all: function all() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var from = params.from, to = params.to, count = params.count, skip = params.skip;
          var expand = void 0;
          if (from) {
            from = normalizeDate(from);
          }
          if (to) {
            to = normalizeDate(to);
          }
          if (params.hasOwnProperty("expand[]")) {
            expand = { "expand[]": params["expand[]"] };
          }
          count = Number(count) || 10;
          skip = Number(skip) || 0;
          return api.get({
            url: "" + BASE_URL,
            data: {
              from,
              to,
              count,
              skip,
              expand
            }
          }, callback);
        },
        fetch: function fetch2(paymentId) {
          var params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
          var callback = arguments[2];
          var expand = void 0;
          if (!paymentId) {
            throw new Error("`payment_id` is mandatory");
          }
          if (params.hasOwnProperty("expand[]")) {
            expand = { "expand[]": params["expand[]"] };
          }
          return api.get({
            url: BASE_URL + "/" + paymentId,
            data: {
              expand
            }
          }, callback);
        },
        capture: function capture(paymentId, amount, currency, callback) {
          if (!paymentId) {
            throw new Error("`payment_id` is mandatory");
          }
          if (!amount) {
            throw new Error("`amount` is mandatory");
          }
          var payload = {
            amount
          };
          if (typeof currency === "function" && !callback) {
            callback = currency;
            currency = void 0;
          } else if (typeof currency === "string") {
            payload.currency = currency;
          }
          return api.post({
            url: BASE_URL + "/" + paymentId + "/capture",
            data: payload
          }, callback);
        },
        createPaymentJson: function createPaymentJson(params, callback) {
          var url = BASE_URL + "/create/json", rest = _objectWithoutProperties(params, []), data = Object.assign(rest);
          return api.post({
            url,
            data
          }, callback);
        },
        createRecurringPayment: function createRecurringPayment(params, callback) {
          return api.post({
            url: BASE_URL + "/create/recurring",
            data: params
          }, callback);
        },
        edit: function edit(paymentId) {
          var params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
          var callback = arguments[2];
          if (!paymentId) {
            throw new Error("`payment_id` is mandatory");
          }
          return api.patch({
            url: BASE_URL + "/" + paymentId,
            data: params
          }, callback);
        },
        refund: function refund(paymentId) {
          var params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
          var callback = arguments[2];
          if (!paymentId) {
            throw new Error("`payment_id` is mandatory");
          }
          return api.post({
            url: BASE_URL + "/" + paymentId + "/refund",
            data: params
          }, callback);
        },
        fetchMultipleRefund: function fetchMultipleRefund(paymentId) {
          var params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
          var callback = arguments[2];
          var from = params.from, to = params.to, count = params.count, skip = params.skip, url = BASE_URL + "/" + paymentId + "/refunds";
          return api.get({
            url,
            data: _extends({}, params, {
              from,
              to,
              count,
              skip
            })
          }, callback);
        },
        fetchRefund: function fetchRefund(paymentId, refundId, callback) {
          if (!paymentId) {
            throw new Error("payment Id` is mandatory");
          }
          if (!refundId) {
            throw new Error("refund Id` is mandatory");
          }
          return api.get({
            url: BASE_URL + "/" + paymentId + "/refunds/" + refundId
          }, callback);
        },
        fetchTransfer: function fetchTransfer(paymentId, callback) {
          if (!paymentId) {
            throw new Error("payment Id` is mandatory");
          }
          return api.get({
            url: BASE_URL + "/" + paymentId + "/transfers"
          }, callback);
        },
        transfer: function transfer(paymentId) {
          var params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
          var callback = arguments[2];
          if (!paymentId) {
            throw new Error("`payment_id` is mandatory");
          }
          return api.post({
            url: BASE_URL + "/" + paymentId + "/transfers",
            data: params
          }, callback);
        },
        bankTransfer: function bankTransfer(paymentId, callback) {
          if (!paymentId) {
            return Promise.reject(ID_REQUIRED_MSG);
          }
          return api.get({
            url: BASE_URL + "/" + paymentId + "/bank_transfer"
          }, callback);
        },
        fetchCardDetails: function fetchCardDetails(paymentId, callback) {
          if (!paymentId) {
            return Promise.reject(ID_REQUIRED_MSG);
          }
          return api.get({
            url: BASE_URL + "/" + paymentId + "/card"
          }, callback);
        },
        fetchPaymentDowntime: function fetchPaymentDowntime(callback) {
          return api.get({
            url: BASE_URL + "/downtimes"
          }, callback);
        },
        fetchPaymentDowntimeById: function fetchPaymentDowntimeById(downtimeId, callback) {
          if (!downtimeId) {
            return Promise.reject("Downtime Id is mandatory");
          }
          return api.get({
            url: BASE_URL + "/downtimes/" + downtimeId
          }, callback);
        },
        otpGenerate: function otpGenerate(paymentId, callback) {
          if (!paymentId) {
            return Promise.reject("payment Id is mandatory");
          }
          return api.post({
            url: BASE_URL + "/" + paymentId + "/otp_generate"
          }, callback);
        },
        otpSubmit: function otpSubmit(paymentId) {
          var params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
          var callback = arguments[2];
          if (!paymentId) {
            return Promise.reject("payment Id is mandatory");
          }
          return api.post({
            url: BASE_URL + "/" + paymentId + "/otp/submit",
            data: params
          }, callback);
        },
        otpResend: function otpResend(paymentId, callback) {
          if (!paymentId) {
            return Promise.reject("payment Id is mandatory");
          }
          return api.post({
            url: BASE_URL + "/" + paymentId + "/otp/resend"
          }, callback);
        },
        createUpi: function createUpi() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var url = BASE_URL + "/create/upi", rest = _objectWithoutProperties(params, []), data = Object.assign(rest);
          return api.post({
            url,
            data
          }, callback);
        },
        validateVpa: function validateVpa() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var url = BASE_URL + "/validate/vpa", rest = _objectWithoutProperties(params, []), data = Object.assign(rest);
          return api.post({
            url,
            data
          }, callback);
        },
        fetchPaymentMethods: function fetchPaymentMethods(callback) {
          var url = "/methods";
          return api.get({
            url
          }, callback);
        }
      };
    };
  }
});

// node_modules/razorpay/dist/resources/refunds.js
var require_refunds = __commonJS({
  "node_modules/razorpay/dist/resources/refunds.js"(exports, module) {
    "use strict";
    var _require = require_razorpay_utils();
    var normalizeDate = _require.normalizeDate;
    var normalizeNotes = _require.normalizeNotes;
    module.exports = function(api) {
      return {
        all: function all() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var from = params.from, to = params.to, count = params.count, skip = params.skip, payment_id = params.payment_id;
          var url = "/refunds";
          if (payment_id) {
            url = "/payments/" + payment_id + "/refunds";
          }
          if (from) {
            from = normalizeDate(from);
          }
          if (to) {
            to = normalizeDate(to);
          }
          count = Number(count) || 10;
          skip = Number(skip) || 0;
          return api.get({
            url,
            data: {
              from,
              to,
              count,
              skip
            }
          }, callback);
        },
        edit: function edit(refundId, params, callback) {
          if (!refundId) {
            throw new Error("refund Id is mandatory");
          }
          return api.patch({
            url: "/refunds/" + refundId,
            data: params
          }, callback);
        },
        fetch: function fetch2(refundId) {
          var params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
          var callback = arguments[2];
          var payment_id = params.payment_id;
          if (!refundId) {
            throw new Error("`refund_id` is mandatory");
          }
          var url = "/refunds/" + refundId;
          if (payment_id) {
            url = "/payments/" + payment_id + url;
          }
          return api.get({
            url
          }, callback);
        }
      };
    };
  }
});

// node_modules/razorpay/dist/resources/orders.js
var require_orders = __commonJS({
  "node_modules/razorpay/dist/resources/orders.js"(exports, module) {
    "use strict";
    var _extends = Object.assign || function(target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i];
        for (var key in source) {
          if (Object.prototype.hasOwnProperty.call(source, key)) {
            target[key] = source[key];
          }
        }
      }
      return target;
    };
    function _objectWithoutProperties(obj, keys) {
      var target = {};
      for (var i in obj) {
        if (keys.indexOf(i) >= 0) continue;
        if (!Object.prototype.hasOwnProperty.call(obj, i)) continue;
        target[i] = obj[i];
      }
      return target;
    }
    var _require = require_razorpay_utils();
    var normalizeDate = _require.normalizeDate;
    module.exports = function(api) {
      return {
        all: function all() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var from = params.from, to = params.to, count = params.count, skip = params.skip, authorized = params.authorized, receipt = params.receipt;
          var expand = void 0;
          if (from) {
            from = normalizeDate(from);
          }
          if (to) {
            to = normalizeDate(to);
          }
          if (params.hasOwnProperty("expand[]")) {
            expand = { "expand[]": params["expand[]"] };
          }
          count = Number(count) || 10;
          skip = Number(skip) || 0;
          authorized = authorized;
          return api.get({
            url: "/orders",
            data: {
              from,
              to,
              count,
              skip,
              authorized,
              receipt,
              expand
            }
          }, callback);
        },
        fetch: function fetch2(orderId, callback) {
          if (!orderId) {
            throw new Error("`order_id` is mandatory");
          }
          return api.get({
            url: "/orders/" + orderId
          }, callback);
        },
        create: function create() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var currency = params.currency, otherParams = _objectWithoutProperties(params, ["currency"]);
          currency = currency || "INR";
          var data = Object.assign(_extends({
            currency
          }, otherParams));
          return api.post({
            url: "/orders",
            data
          }, callback);
        },
        edit: function edit(orderId) {
          var params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
          var callback = arguments[2];
          if (!orderId) {
            throw new Error("`order_id` is mandatory");
          }
          return api.patch({
            url: "/orders/" + orderId,
            data: params
          }, callback);
        },
        fetchPayments: function fetchPayments(orderId, callback) {
          if (!orderId) {
            throw new Error("`order_id` is mandatory");
          }
          return api.get({
            url: "/orders/" + orderId + "/payments"
          }, callback);
        },
        fetchTransferOrder: function fetchTransferOrder(orderId, callback) {
          if (!orderId) {
            throw new Error("`order_id` is mandatory");
          }
          return api.get({
            url: "/orders/" + orderId + "/?expand[]=transfers&status"
          }, callback);
        },
        viewRtoReview: function viewRtoReview(orderId, callback) {
          return api.post({
            url: "/orders/" + orderId + "/rto_review"
          }, callback);
        },
        editFulfillment: function editFulfillment(orderId) {
          var param = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
          var callback = arguments[2];
          return api.post({
            url: "/orders/" + orderId + "/fulfillment",
            data: param
          });
        }
      };
    };
  }
});

// node_modules/razorpay/dist/resources/customers.js
var require_customers = __commonJS({
  "node_modules/razorpay/dist/resources/customers.js"(exports, module) {
    "use strict";
    module.exports = function(api) {
      return {
        create: function create(params, callback) {
          return api.post({
            url: "/customers",
            data: params
          }, callback);
        },
        edit: function edit(customerId, params, callback) {
          return api.put({
            url: "/customers/" + customerId,
            data: params
          }, callback);
        },
        fetch: function fetch2(customerId, callback) {
          return api.get({
            url: "/customers/" + customerId
          }, callback);
        },
        all: function all() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var count = params.count, skip = params.skip;
          count = Number(count) || 10;
          skip = Number(skip) || 0;
          return api.get({
            url: "/customers",
            data: {
              count,
              skip
            }
          }, callback);
        },
        fetchTokens: function fetchTokens(customerId, callback) {
          return api.get({
            url: "/customers/" + customerId + "/tokens"
          }, callback);
        },
        fetchToken: function fetchToken(customerId, tokenId, callback) {
          return api.get({
            url: "/customers/" + customerId + "/tokens/" + tokenId
          }, callback);
        },
        deleteToken: function deleteToken(customerId, tokenId, callback) {
          return api.delete({
            url: "/customers/" + customerId + "/tokens/" + tokenId
          }, callback);
        },
        addBankAccount: function addBankAccount(customerId, params, callback) {
          return api.post({
            url: "/customers/" + customerId + "/bank_account",
            data: params
          }, callback);
        },
        deleteBankAccount: function deleteBankAccount(customerId, bankId, callback) {
          return api.delete({
            url: "/customers/" + customerId + "/bank_account/" + bankId
          }, callback);
        },
        requestEligibilityCheck: function requestEligibilityCheck(params, callback) {
          return api.post({
            url: "/customers/eligibility",
            data: params
          }, callback);
        },
        fetchEligibility: function fetchEligibility(eligibilityId, callback) {
          return api.get({
            url: "/customers/eligibility/" + eligibilityId
          }, callback);
        }
      };
    };
  }
});

// node_modules/razorpay/dist/resources/transfers.js
var require_transfers = __commonJS({
  "node_modules/razorpay/dist/resources/transfers.js"(exports, module) {
    "use strict";
    var _require = require_razorpay_utils();
    var normalizeDate = _require.normalizeDate;
    module.exports = function(api) {
      return {
        all: function all() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var from = params.from, to = params.to, count = params.count, skip = params.skip, payment_id = params.payment_id, recipient_settlement_id = params.recipient_settlement_id;
          var url = "/transfers";
          if (payment_id) {
            url = "/payments/" + payment_id + "/transfers";
          }
          if (from) {
            from = normalizeDate(from);
          }
          if (to) {
            to = normalizeDate(to);
          }
          count = Number(count) || 10;
          skip = Number(skip) || 0;
          return api.get({
            url,
            data: {
              from,
              to,
              count,
              skip,
              recipient_settlement_id
            }
          }, callback);
        },
        fetch: function fetch2(transferId) {
          var params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
          var callback = arguments[2];
          var payment_id = params.payment_id;
          if (!transferId) {
            throw new Error("`transfer_id` is mandatory");
          }
          var url = "/transfers/" + transferId;
          return api.get({
            url
          }, callback);
        },
        create: function create(params, callback) {
          return api.post({
            url: "/transfers",
            data: params
          }, callback);
        },
        edit: function edit(transferId, params, callback) {
          return api.patch({
            url: "/transfers/" + transferId,
            data: params
          }, callback);
        },
        reverse: function reverse(transferId, params, callback) {
          if (!transferId) {
            throw new Error("`transfer_id` is mandatory");
          }
          var url = "/transfers/" + transferId + "/reversals";
          return api.post({
            url,
            data: params
          }, callback);
        },
        fetchSettlements: function fetchSettlements(callback) {
          return api.get({
            url: "/transfers?expand[]=recipient_settlement"
          }, callback);
        }
      };
    };
  }
});

// node_modules/razorpay/dist/resources/tokens.js
var require_tokens = __commonJS({
  "node_modules/razorpay/dist/resources/tokens.js"(exports, module) {
    "use strict";
    var _require = require_razorpay_utils();
    var normalizeNotes = _require.normalizeNotes;
    module.exports = function(api) {
      var BASE_URL = "/tokens";
      return {
        create: function create(params, callback) {
          return api.post({
            url: "" + BASE_URL,
            data: params
          }, callback);
        },
        fetch: function fetch2(params, callback) {
          return api.post({
            url: BASE_URL + "/fetch",
            data: params
          }, callback);
        },
        delete: function _delete(params, callback) {
          return api.post({
            url: BASE_URL + "/delete",
            data: params
          }, callback);
        },
        processPaymentOnAlternatePAorPG: function processPaymentOnAlternatePAorPG(params, callback) {
          return api.post({
            url: BASE_URL + "/service_provider_tokens/token_transactional_data",
            data: params
          }, callback);
        }
      };
    };
  }
});

// node_modules/razorpay/dist/resources/virtualAccounts.js
var require_virtualAccounts = __commonJS({
  "node_modules/razorpay/dist/resources/virtualAccounts.js"(exports, module) {
    "use strict";
    var _extends = Object.assign || function(target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i];
        for (var key in source) {
          if (Object.prototype.hasOwnProperty.call(source, key)) {
            target[key] = source[key];
          }
        }
      }
      return target;
    };
    function _objectWithoutProperties(obj, keys) {
      var target = {};
      for (var i in obj) {
        if (keys.indexOf(i) >= 0) continue;
        if (!Object.prototype.hasOwnProperty.call(obj, i)) continue;
        target[i] = obj[i];
      }
      return target;
    }
    var _require = require_razorpay_utils();
    var normalizeDate = _require.normalizeDate;
    var normalizeNotes = _require.normalizeNotes;
    var BASE_URL = "/virtual_accounts";
    var ID_REQUIRED_MSG = "`virtual_account_id` is mandatory";
    module.exports = function(api) {
      return {
        all: function all() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var from = params.from, to = params.to, count = params.count, skip = params.skip, otherParams = _objectWithoutProperties(params, ["from", "to", "count", "skip"]);
          var url = BASE_URL;
          if (from) {
            from = normalizeDate(from);
          }
          if (to) {
            to = normalizeDate(to);
          }
          count = Number(count) || 10;
          skip = Number(skip) || 0;
          return api.get({
            url,
            data: _extends({
              from,
              to,
              count,
              skip
            }, otherParams)
          }, callback);
        },
        fetch: function fetch2(virtualAccountId, callback) {
          if (!virtualAccountId) {
            return Promise.reject(ID_REQUIRED_MSG);
          }
          var url = BASE_URL + "/" + virtualAccountId;
          return api.get({
            url
          }, callback);
        },
        create: function create() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          return api.post({
            url: BASE_URL,
            data: params
          }, callback);
        },
        close: function close(virtualAccountId, callback) {
          if (!virtualAccountId) {
            return Promise.reject(ID_REQUIRED_MSG);
          }
          return api.post({
            url: BASE_URL + "/" + virtualAccountId + "/close"
          }, callback);
        },
        fetchPayments: function fetchPayments(virtualAccountId, callback) {
          if (!virtualAccountId) {
            return Promise.reject(ID_REQUIRED_MSG);
          }
          var url = BASE_URL + "/" + virtualAccountId + "/payments";
          return api.get({
            url
          }, callback);
        },
        addReceiver: function addReceiver(virtualAccountId) {
          var params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
          var callback = arguments[2];
          if (!virtualAccountId) {
            return Promise.reject(ID_REQUIRED_MSG);
          }
          return api.post({
            url: BASE_URL + "/" + virtualAccountId + "/receivers",
            data: params
          }, callback);
        },
        allowedPayer: function allowedPayer(virtualAccountId) {
          var params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
          var callback = arguments[2];
          if (!virtualAccountId) {
            return Promise.reject(ID_REQUIRED_MSG);
          }
          return api.post({
            url: BASE_URL + "/" + virtualAccountId + "/allowed_payers",
            data: params
          }, callback);
        },
        deleteAllowedPayer: function deleteAllowedPayer(virtualAccountId, allowedPayerId, callback) {
          if (!virtualAccountId) {
            return Promise.reject(ID_REQUIRED_MSG);
          }
          if (!allowedPayerId) {
            return Promise.reject("allowed payer id is mandatory");
          }
          return api.delete({
            url: BASE_URL + "/" + virtualAccountId + "/allowed_payers/" + allowedPayerId
          }, callback);
        }
      };
    };
  }
});

// node_modules/razorpay/dist/resources/invoices.js
var require_invoices = __commonJS({
  "node_modules/razorpay/dist/resources/invoices.js"(exports, module) {
    "use strict";
    var _extends = Object.assign || function(target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i];
        for (var key in source) {
          if (Object.prototype.hasOwnProperty.call(source, key)) {
            target[key] = source[key];
          }
        }
      }
      return target;
    };
    var _require = require_razorpay_utils();
    var normalizeDate = _require.normalizeDate;
    module.exports = function invoicesApi(api) {
      var BASE_URL = "/invoices", MISSING_ID_ERROR = "Invoice ID is mandatory";
      return {
        create: function create() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var url = BASE_URL;
          return api.post({
            url,
            data: params
          }, callback);
        },
        edit: function edit(invoiceId) {
          var params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
          var callback = arguments[2];
          var url = BASE_URL + "/" + invoiceId;
          if (!invoiceId) {
            return Promise.reject("Invoice ID is mandatory");
          }
          return api.patch({
            url,
            data: params
          }, callback);
        },
        issue: function issue(invoiceId, callback) {
          if (!invoiceId) {
            return Promise.reject(MISSING_ID_ERROR);
          }
          var url = BASE_URL + "/" + invoiceId + "/issue";
          return api.post({
            url
          }, callback);
        },
        delete: function _delete(invoiceId, callback) {
          if (!invoiceId) {
            return Promise.reject(MISSING_ID_ERROR);
          }
          var url = BASE_URL + "/" + invoiceId;
          return api.delete({
            url
          }, callback);
        },
        cancel: function cancel(invoiceId, callback) {
          if (!invoiceId) {
            return Promise.reject(MISSING_ID_ERROR);
          }
          var url = BASE_URL + "/" + invoiceId + "/cancel";
          return api.post({
            url
          }, callback);
        },
        fetch: function fetch2(invoiceId, callback) {
          if (!invoiceId) {
            return Promise.reject(MISSING_ID_ERROR);
          }
          var url = BASE_URL + "/" + invoiceId;
          return api.get({
            url
          }, callback);
        },
        all: function all() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var from = params.from, to = params.to, count = params.count, skip = params.skip, url = BASE_URL;
          if (from) {
            from = normalizeDate(from);
          }
          if (to) {
            to = normalizeDate(to);
          }
          count = Number(count) || 10;
          skip = Number(skip) || 0;
          return api.get({
            url,
            data: _extends({}, params, {
              from,
              to,
              count,
              skip
            })
          }, callback);
        },
        notifyBy: function notifyBy(invoiceId, medium, callback) {
          if (!invoiceId) {
            return Promise.reject(MISSING_ID_ERROR);
          }
          if (!medium) {
            return Promise.reject("`medium` is required");
          }
          var url = BASE_URL + "/" + invoiceId + "/notify_by/" + medium;
          return api.post({
            url
          }, callback);
        }
      };
    };
  }
});

// node_modules/razorpay/dist/resources/iins.js
var require_iins = __commonJS({
  "node_modules/razorpay/dist/resources/iins.js"(exports, module) {
    "use strict";
    module.exports = function(api) {
      var BASE_URL = "/iins";
      return {
        fetch: function fetch2(tokenIin, callback) {
          return api.get({
            url: BASE_URL + "/" + tokenIin
          }, callback);
        },
        all: function all() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          return api.get({
            url: BASE_URL + "/list",
            data: params
          }, callback);
        }
      };
    };
  }
});

// node_modules/razorpay/dist/resources/paymentLink.js
var require_paymentLink = __commonJS({
  "node_modules/razorpay/dist/resources/paymentLink.js"(exports, module) {
    "use strict";
    var _extends = Object.assign || function(target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i];
        for (var key in source) {
          if (Object.prototype.hasOwnProperty.call(source, key)) {
            target[key] = source[key];
          }
        }
      }
      return target;
    };
    var _require = require_razorpay_utils();
    var normalizeDate = _require.normalizeDate;
    module.exports = function paymentLinkApi(api) {
      var BASE_URL = "/payment_links", MISSING_ID_ERROR = "Payment Link ID is mandatory";
      return {
        create: function create(params, callback) {
          var url = BASE_URL;
          return api.post({
            url,
            data: params
          }, callback);
        },
        cancel: function cancel(paymentLinkId, callback) {
          if (!paymentLinkId) {
            return Promise.reject(MISSING_ID_ERROR);
          }
          var url = BASE_URL + "/" + paymentLinkId + "/cancel";
          return api.post({
            url
          }, callback);
        },
        fetch: function fetch2(paymentLinkId, callback) {
          if (!paymentLinkId) {
            return Promise.reject(MISSING_ID_ERROR);
          }
          var url = BASE_URL + "/" + paymentLinkId;
          return api.get({
            url
          }, callback);
        },
        all: function all() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var from = params.from, to = params.to, count = params.count, skip = params.skip, url = BASE_URL;
          if (from) {
            from = normalizeDate(from);
          }
          if (to) {
            to = normalizeDate(to);
          }
          count = Number(count) || 10;
          skip = Number(skip) || 0;
          return api.get({
            url,
            data: _extends({}, params, {
              from,
              to,
              count,
              skip
            })
          }, callback);
        },
        edit: function edit(paymentLinkId, params, callback) {
          return api.patch({
            url: BASE_URL + "/" + paymentLinkId,
            data: params
          }, callback);
        },
        notifyBy: function notifyBy(paymentLinkId, medium, callback) {
          if (!paymentLinkId) {
            return Promise.reject(MISSING_ID_ERROR);
          }
          if (!medium) {
            return Promise.reject("`medium` is required");
          }
          var url = BASE_URL + "/" + paymentLinkId + "/notify_by/" + medium;
          return api.post({
            url
          }, callback);
        }
      };
    };
  }
});

// node_modules/razorpay/dist/resources/plans.js
var require_plans = __commonJS({
  "node_modules/razorpay/dist/resources/plans.js"(exports, module) {
    "use strict";
    var _extends = Object.assign || function(target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i];
        for (var key in source) {
          if (Object.prototype.hasOwnProperty.call(source, key)) {
            target[key] = source[key];
          }
        }
      }
      return target;
    };
    var _require = require_razorpay_utils();
    var normalizeDate = _require.normalizeDate;
    module.exports = function plansApi(api) {
      var BASE_URL = "/plans", MISSING_ID_ERROR = "Plan ID is mandatory";
      return {
        create: function create() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var url = BASE_URL;
          return api.post({
            url,
            data: params
          }, callback);
        },
        fetch: function fetch2(planId, callback) {
          if (!planId) {
            return Promise.reject(MISSING_ID_ERROR);
          }
          var url = BASE_URL + "/" + planId;
          return api.get({ url }, callback);
        },
        all: function all() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var from = params.from, to = params.to, count = params.count, skip = params.skip, url = BASE_URL;
          if (from) {
            from = normalizeDate(from);
          }
          if (to) {
            to = normalizeDate(to);
          }
          count = Number(count) || 10;
          skip = Number(skip) || 0;
          return api.get({
            url,
            data: _extends({}, params, {
              from,
              to,
              count,
              skip
            })
          }, callback);
        }
      };
    };
  }
});

// node_modules/razorpay/dist/resources/products.js
var require_products = __commonJS({
  "node_modules/razorpay/dist/resources/products.js"(exports, module) {
    "use strict";
    module.exports = function(api) {
      var BASE_URL = "/accounts";
      return {
        requestProductConfiguration: function requestProductConfiguration(accountId, params, callback) {
          return api.post({
            version: "v2",
            url: BASE_URL + "/" + accountId + "/products",
            data: params
          }, callback);
        },
        edit: function edit(accountId, productId, params, callback) {
          return api.patch({
            version: "v2",
            url: BASE_URL + "/" + accountId + "/products/" + productId,
            data: params
          }, callback);
        },
        fetch: function fetch2(accountId, productId, callback) {
          return api.get({
            version: "v2",
            url: BASE_URL + "/" + accountId + "/products/" + productId
          }, callback);
        },
        fetchTnc: function fetchTnc(productName, callback) {
          return api.get({
            version: "v2",
            url: "/products/" + productName + "/tnc"
          }, callback);
        }
      };
    };
  }
});

// node_modules/razorpay/dist/resources/subscriptions.js
var require_subscriptions = __commonJS({
  "node_modules/razorpay/dist/resources/subscriptions.js"(exports, module) {
    "use strict";
    var _extends = Object.assign || function(target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i];
        for (var key in source) {
          if (Object.prototype.hasOwnProperty.call(source, key)) {
            target[key] = source[key];
          }
        }
      }
      return target;
    };
    var _require = require_razorpay_utils();
    var normalizeDate = _require.normalizeDate;
    module.exports = function subscriptionsApi(api) {
      var BASE_URL = "/subscriptions", MISSING_ID_ERROR = "Subscription ID is mandatory";
      return {
        create: function create() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var url = BASE_URL;
          return api.post({
            url,
            data: params
          }, callback);
        },
        fetch: function fetch2(subscriptionId, callback) {
          if (!subscriptionId) {
            return Promise.reject(MISSING_ID_ERROR);
          }
          var url = BASE_URL + "/" + subscriptionId;
          return api.get({ url }, callback);
        },
        update: function update(subscriptionId, params, callback) {
          var url = BASE_URL + "/" + subscriptionId;
          if (!subscriptionId) {
            return Promise.reject(MISSING_ID_ERROR);
          }
          return api.patch({
            url,
            data: params
          }, callback);
        },
        pendingUpdate: function pendingUpdate(subscriptionId, callback) {
          var url = BASE_URL + "/" + subscriptionId + "/retrieve_scheduled_changes";
          if (!subscriptionId) {
            return Promise.reject(MISSING_ID_ERROR);
          }
          return api.get({ url }, callback);
        },
        cancelScheduledChanges: function cancelScheduledChanges(subscriptionId, callback) {
          var url = BASE_URL + "/" + subscriptionId + "/cancel_scheduled_changes";
          if (!subscriptionId) {
            return Promise.reject("Subscription Id is mandatory");
          }
          return api.post({
            url
          }, callback);
        },
        pause: function pause(subscriptionId) {
          var params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
          var callback = arguments[2];
          var url = BASE_URL + "/" + subscriptionId + "/pause";
          if (!subscriptionId) {
            return Promise.reject("Subscription Id is mandatory");
          }
          return api.post({
            url,
            data: params
          }, callback);
        },
        resume: function resume(subscriptionId) {
          var params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
          var callback = arguments[2];
          var url = BASE_URL + "/" + subscriptionId + "/resume";
          if (!subscriptionId) {
            return Promise.reject("Subscription Id is mandatory");
          }
          return api.post({
            url,
            data: params
          }, callback);
        },
        deleteOffer: function deleteOffer(subscriptionId, offerId, callback) {
          var url = BASE_URL + "/" + subscriptionId + "/" + offerId;
          if (!subscriptionId) {
            return Promise.reject("Subscription Id is mandatory");
          }
          return api.delete({
            url
          }, callback);
        },
        all: function all() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var from = params.from, to = params.to, count = params.count, skip = params.skip, url = BASE_URL;
          if (from) {
            from = normalizeDate(from);
          }
          if (to) {
            to = normalizeDate(to);
          }
          count = Number(count) || 10;
          skip = Number(skip) || 0;
          return api.get({
            url,
            data: _extends({}, params, {
              from,
              to,
              count,
              skip
            })
          }, callback);
        },
        cancel: function cancel(subscriptionId) {
          var cancelAtCycleEnd = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;
          var callback = arguments[2];
          var url = BASE_URL + "/" + subscriptionId + "/cancel";
          if (!subscriptionId) {
            return Promise.reject(MISSING_ID_ERROR);
          }
          return api.post(_extends({
            url
          }, cancelAtCycleEnd && { data: { cancel_at_cycle_end: 1 } }), callback);
        },
        createAddon: function createAddon(subscriptionId, params, callback) {
          var url = BASE_URL + "/" + subscriptionId + "/addons";
          if (!subscriptionId) {
            return Promise.reject(MISSING_ID_ERROR);
          }
          return api.post({
            url,
            data: _extends({}, params)
          }, callback);
        },
        createRegistrationLink: function createRegistrationLink() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          return api.post({
            url: "/subscription_registration/auth_links",
            data: params
          }, callback);
        }
      };
    };
  }
});

// node_modules/razorpay/dist/resources/addons.js
var require_addons = __commonJS({
  "node_modules/razorpay/dist/resources/addons.js"(exports, module) {
    "use strict";
    var _extends = Object.assign || function(target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i];
        for (var key in source) {
          if (Object.prototype.hasOwnProperty.call(source, key)) {
            target[key] = source[key];
          }
        }
      }
      return target;
    };
    var _require = require_razorpay_utils();
    var normalizeDate = _require.normalizeDate;
    module.exports = function(api) {
      var BASE_URL = "/addons", MISSING_ID_ERROR = "Addon ID is mandatory";
      return {
        fetch: function fetch2(addonId, callback) {
          if (!addonId) {
            return Promise.reject(MISSING_ID_ERROR);
          }
          var url = BASE_URL + "/" + addonId;
          return api.get({
            url
          }, callback);
        },
        delete: function _delete(addonId, callback) {
          if (!addonId) {
            return Promise.reject(MISSING_ID_ERROR);
          }
          var url = BASE_URL + "/" + addonId;
          return api.delete({
            url
          }, callback);
        },
        all: function all() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var from = params.from, to = params.to, count = params.count, skip = params.skip, url = BASE_URL;
          if (from) {
            from = normalizeDate(from);
          }
          if (to) {
            to = normalizeDate(to);
          }
          count = Number(count) || 10;
          skip = Number(skip) || 0;
          return api.get({
            url,
            data: _extends({}, params, {
              from,
              to,
              count,
              skip
            })
          }, callback);
        }
      };
    };
  }
});

// node_modules/razorpay/dist/resources/settlements.js
var require_settlements = __commonJS({
  "node_modules/razorpay/dist/resources/settlements.js"(exports, module) {
    "use strict";
    var _extends = Object.assign || function(target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i];
        for (var key in source) {
          if (Object.prototype.hasOwnProperty.call(source, key)) {
            target[key] = source[key];
          }
        }
      }
      return target;
    };
    module.exports = function(api) {
      var BASE_URL = "/settlements";
      return {
        createOndemandSettlement: function createOndemandSettlement() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var url = BASE_URL + "/ondemand";
          return api.post({
            url,
            data: params
          }, callback);
        },
        all: function all() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var from = params.from, to = params.to, count = params.count, skip = params.skip, url = BASE_URL;
          return api.get({
            url,
            data: _extends({}, params, {
              from,
              to,
              count,
              skip
            })
          }, callback);
        },
        fetch: function fetch2(settlementId, callback) {
          if (!settlementId) {
            return Promise.reject("settlement Id is mandatroy");
          }
          return api.get({
            url: BASE_URL + "/" + settlementId
          }, callback);
        },
        fetchOndemandSettlementById: function fetchOndemandSettlementById(settlementId) {
          var param = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
          var callback = arguments[2];
          var expand = void 0;
          if (!settlementId) {
            return Promise.reject("settlment Id is mandatroy");
          }
          if (param.hasOwnProperty("expand[]")) {
            expand = { "expand[]": param["expand[]"] };
          }
          return api.get({
            url: BASE_URL + "/ondemand/" + settlementId,
            data: {
              expand
            }
          }, callback);
        },
        fetchAllOndemandSettlement: function fetchAllOndemandSettlement() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var expand = void 0;
          var from = params.from, to = params.to, count = params.count, skip = params.skip, url = BASE_URL + "/ondemand";
          if (params.hasOwnProperty("expand[]")) {
            expand = { "expand[]": params["expand[]"] };
          }
          return api.get({
            url,
            data: _extends({}, params, {
              from,
              to,
              count,
              skip,
              expand
            })
          }, callback);
        },
        reports: function reports() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var day = params.day, count = params.count, skip = params.skip, url = BASE_URL + "/recon/combined";
          return api.get({
            url,
            data: _extends({}, params, {
              day,
              count,
              skip
            })
          }, callback);
        }
      };
    };
  }
});

// node_modules/razorpay/dist/resources/qrCode.js
var require_qrCode = __commonJS({
  "node_modules/razorpay/dist/resources/qrCode.js"(exports, module) {
    "use strict";
    var _extends = Object.assign || function(target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i];
        for (var key in source) {
          if (Object.prototype.hasOwnProperty.call(source, key)) {
            target[key] = source[key];
          }
        }
      }
      return target;
    };
    module.exports = function(api) {
      var BASE_URL = "/payments/qr_codes";
      return {
        create: function create() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var url = BASE_URL;
          return api.post({
            url,
            data: params
          }, callback);
        },
        all: function all() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var from = params.from, to = params.to, count = params.count, skip = params.skip, url = BASE_URL;
          return api.get({
            url,
            data: _extends({}, params, {
              from,
              to,
              count,
              skip
            })
          }, callback);
        },
        fetchAllPayments: function fetchAllPayments(qrCodeId) {
          var params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
          var callback = arguments[2];
          var from = params.from, to = params.to, count = params.count, skip = params.skip, url = BASE_URL + "/" + qrCodeId + "/payments";
          return api.get({
            url,
            data: _extends({}, params, {
              from,
              to,
              count,
              skip
            })
          }, callback);
        },
        fetch: function fetch2(qrCodeId, callback) {
          if (!qrCodeId) {
            return Promise.reject("qrCode Id is mandatroy");
          }
          return api.get({
            url: BASE_URL + "/" + qrCodeId
          }, callback);
        },
        close: function close(qrCodeId, callback) {
          if (!qrCodeId) {
            return Promise.reject("qrCode Id is mandatroy");
          }
          var url = BASE_URL + "/" + qrCodeId + "/close";
          return api.post({
            url
          }, callback);
        }
      };
    };
  }
});

// node_modules/razorpay/dist/resources/fundAccount.js
var require_fundAccount = __commonJS({
  "node_modules/razorpay/dist/resources/fundAccount.js"(exports, module) {
    "use strict";
    var _extends = Object.assign || function(target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i];
        for (var key in source) {
          if (Object.prototype.hasOwnProperty.call(source, key)) {
            target[key] = source[key];
          }
        }
      }
      return target;
    };
    module.exports = function(api) {
      return {
        create: function create(params, callback) {
          return api.post({
            url: "/fund_accounts",
            data: _extends({}, params)
          }, callback);
        },
        fetch: function fetch2(customerId, callback) {
          if (!customerId) {
            return Promise.reject("Customer Id is mandatroy");
          }
          return api.get({
            url: "/fund_accounts?customer_id=" + customerId
          }, callback);
        }
      };
    };
  }
});

// node_modules/razorpay/dist/resources/items.js
var require_items = __commonJS({
  "node_modules/razorpay/dist/resources/items.js"(exports, module) {
    "use strict";
    var _extends = Object.assign || function(target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i];
        for (var key in source) {
          if (Object.prototype.hasOwnProperty.call(source, key)) {
            target[key] = source[key];
          }
        }
      }
      return target;
    };
    function _objectWithoutProperties(obj, keys) {
      var target = {};
      for (var i in obj) {
        if (keys.indexOf(i) >= 0) continue;
        if (!Object.prototype.hasOwnProperty.call(obj, i)) continue;
        target[i] = obj[i];
      }
      return target;
    }
    var _require = require_razorpay_utils();
    var normalizeDate = _require.normalizeDate;
    module.exports = function(api) {
      return {
        all: function all() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var from = params.from, to = params.to, count = params.count, skip = params.skip, authorized = params.authorized, receipt = params.receipt;
          if (from) {
            from = normalizeDate(from);
          }
          if (to) {
            to = normalizeDate(to);
          }
          count = Number(count) || 10;
          skip = Number(skip) || 0;
          return api.get({
            url: "/items",
            data: {
              from,
              to,
              count,
              skip,
              authorized,
              receipt
            }
          }, callback);
        },
        fetch: function fetch2(itemId, callback) {
          if (!itemId) {
            throw new Error("`item_id` is mandatory");
          }
          return api.get({
            url: "/items/" + itemId
          }, callback);
        },
        create: function create() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var amount = params.amount, currency = params.currency, rest = _objectWithoutProperties(params, ["amount", "currency"]);
          currency = currency || "INR";
          if (!amount) {
            throw new Error("`amount` is mandatory");
          }
          var data = Object.assign(_extends({
            currency,
            amount
          }, rest));
          return api.post({
            url: "/items",
            data
          }, callback);
        },
        edit: function edit(itemId) {
          var params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
          var callback = arguments[2];
          if (!itemId) {
            throw new Error("`item_id` is mandatory");
          }
          var url = "/items/" + itemId;
          return api.patch({
            url,
            data: params
          }, callback);
        },
        delete: function _delete(itemId, callback) {
          if (!itemId) {
            throw new Error("`item_id` is mandatory");
          }
          return api.delete({
            url: "/items/" + itemId
          }, callback);
        }
      };
    };
  }
});

// node_modules/razorpay/dist/resources/cards.js
var require_cards = __commonJS({
  "node_modules/razorpay/dist/resources/cards.js"(exports, module) {
    "use strict";
    module.exports = function(api) {
      return {
        fetch: function fetch2(itemId, callback) {
          if (!itemId) {
            throw new Error("`card_id` is mandatory");
          }
          return api.get({
            url: "/cards/" + itemId
          }, callback);
        },
        requestCardReference: function requestCardReference(params, callback) {
          return api.post({
            url: "/cards/fingerprints",
            data: params
          }, callback);
        }
      };
    };
  }
});

// node_modules/razorpay/dist/resources/webhooks.js
var require_webhooks = __commonJS({
  "node_modules/razorpay/dist/resources/webhooks.js"(exports, module) {
    "use strict";
    var _extends = Object.assign || function(target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i];
        for (var key in source) {
          if (Object.prototype.hasOwnProperty.call(source, key)) {
            target[key] = source[key];
          }
        }
      }
      return target;
    };
    var _require = require_razorpay_utils();
    var normalizeDate = _require.normalizeDate;
    module.exports = function(api) {
      var BASE_URL = "/accounts";
      return {
        create: function create(params, accountId, callback) {
          var payload = { url: "/webhooks", data: params };
          if (accountId) {
            payload = {
              version: "v2",
              url: BASE_URL + "/" + accountId + "/webhooks",
              data: params
            };
          }
          return api.post(payload, callback);
        },
        edit: function edit(params, webhookId, accountId, callback) {
          if (accountId && webhookId) {
            return api.patch({
              version: "v2",
              url: BASE_URL + "/" + accountId + "/webhooks/" + webhookId,
              data: params
            }, callback);
          }
          return api.put({
            url: "/webhooks/" + webhookId,
            data: params
          }, callback);
        },
        all: function all() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var accountId = arguments[1];
          var callback = arguments[2];
          var from = params.from, to = params.to, count = params.count, skip = params.skip;
          if (from) {
            from = normalizeDate(from);
          }
          if (to) {
            to = normalizeDate(to);
          }
          count = Number(count) || 10;
          skip = Number(skip) || 0;
          var data = _extends({}, params, { from, to, count, skip });
          if (accountId) {
            return api.get({
              version: "v2",
              url: BASE_URL + "/" + accountId + "/webhooks/",
              data
            }, callback);
          }
          return api.get({
            url: "/webhooks",
            data
          }, callback);
        },
        fetch: function fetch2(webhookId, accountId, callback) {
          return api.get({
            version: "v2",
            url: BASE_URL + "/" + accountId + "/webhooks/" + webhookId
          }, callback);
        },
        delete: function _delete(webhookId, accountId, callback) {
          return api.delete({
            version: "v2",
            url: BASE_URL + "/" + accountId + "/webhooks/" + webhookId
          }, callback);
        }
      };
    };
  }
});

// node_modules/razorpay/dist/resources/documents.js
var require_documents = __commonJS({
  "node_modules/razorpay/dist/resources/documents.js"(exports, module) {
    "use strict";
    var _extends = Object.assign || function(target) {
      for (var i = 1; i < arguments.length; i++) {
        var source = arguments[i];
        for (var key in source) {
          if (Object.prototype.hasOwnProperty.call(source, key)) {
            target[key] = source[key];
          }
        }
      }
      return target;
    };
    function _objectWithoutProperties(obj, keys) {
      var target = {};
      for (var i in obj) {
        if (keys.indexOf(i) >= 0) continue;
        if (!Object.prototype.hasOwnProperty.call(obj, i)) continue;
        target[i] = obj[i];
      }
      return target;
    }
    module.exports = function(api) {
      var BASE_URL = "/documents";
      return {
        create: function create(params, callback) {
          var file = params.file, rest = _objectWithoutProperties(params, ["file"]);
          return api.postFormData({
            url: "" + BASE_URL,
            formData: _extends({
              file: file.value
            }, rest)
          }, callback);
        },
        fetch: function fetch2(documentId, callback) {
          return api.get({
            url: BASE_URL + "/" + documentId
          }, callback);
        }
      };
    };
  }
});

// node_modules/razorpay/dist/resources/disputes.js
var require_disputes = __commonJS({
  "node_modules/razorpay/dist/resources/disputes.js"(exports, module) {
    "use strict";
    module.exports = function(api) {
      var BASE_URL = "/disputes";
      return {
        fetch: function fetch2(disputeId, callback) {
          return api.get({
            url: BASE_URL + "/" + disputeId
          }, callback);
        },
        all: function all() {
          var params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
          var callback = arguments[1];
          var count = params.count, skip = params.skip;
          count = Number(count) || 10;
          skip = Number(skip) || 0;
          return api.get({
            url: "" + BASE_URL,
            data: {
              count,
              skip
            }
          }, callback);
        },
        accept: function accept(disputeId, callback) {
          return api.post({
            url: BASE_URL + "/" + disputeId + "/accept"
          }, callback);
        },
        contest: function contest(disputeId, param, callback) {
          return api.patch({
            url: BASE_URL + "/" + disputeId + "/contest",
            data: param
          }, callback);
        }
      };
    };
  }
});

// node_modules/razorpay/dist/razorpay.js
var require_razorpay = __commonJS({
  "node_modules/razorpay/dist/razorpay.js"(exports, module) {
    var _createClass = /* @__PURE__ */ function() {
      function defineProperties(target, props) {
        for (var i = 0; i < props.length; i++) {
          var descriptor = props[i];
          descriptor.enumerable = descriptor.enumerable || false;
          descriptor.configurable = true;
          if ("value" in descriptor) descriptor.writable = true;
          Object.defineProperty(target, descriptor.key, descriptor);
        }
      }
      return function(Constructor, protoProps, staticProps) {
        if (protoProps) defineProperties(Constructor.prototype, protoProps);
        if (staticProps) defineProperties(Constructor, staticProps);
        return Constructor;
      };
    }();
    function _classCallCheck(instance, Constructor) {
      if (!(instance instanceof Constructor)) {
        throw new TypeError("Cannot call a class as a function");
      }
    }
    var API = require_api();
    var pkg = require_package();
    var _require = require_razorpay_utils();
    var _validateWebhookSignature = _require.validateWebhookSignature;
    var Razorpay = function() {
      _createClass(Razorpay2, null, [{
        key: "validateWebhookSignature",
        value: function validateWebhookSignature() {
          return _validateWebhookSignature.apply(void 0, arguments);
        }
      }]);
      function Razorpay2() {
        var options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
        _classCallCheck(this, Razorpay2);
        var key_id = options.key_id, key_secret = options.key_secret, oauthToken = options.oauthToken, headers = options.headers;
        if (!key_id && !oauthToken) {
          throw new Error("`key_id` or `oauthToken` is mandatory");
        }
        this.key_id = key_id;
        this.key_secret = key_secret;
        this.oauthToken = oauthToken;
        this.api = new API({
          hostUrl: "https://api.razorpay.com",
          ua: "razorpay-node@" + Razorpay2.VERSION,
          key_id,
          key_secret,
          headers,
          oauthToken
        });
        this.addResources();
      }
      _createClass(Razorpay2, [{
        key: "addResources",
        value: function addResources() {
          Object.assign(this, {
            accounts: require_accounts()(this.api),
            stakeholders: require_stakeholders()(this.api),
            payments: require_payments()(this.api),
            refunds: require_refunds()(this.api),
            orders: require_orders()(this.api),
            customers: require_customers()(this.api),
            transfers: require_transfers()(this.api),
            tokens: require_tokens()(this.api),
            virtualAccounts: require_virtualAccounts()(this.api),
            invoices: require_invoices()(this.api),
            iins: require_iins()(this.api),
            paymentLink: require_paymentLink()(this.api),
            plans: require_plans()(this.api),
            products: require_products()(this.api),
            subscriptions: require_subscriptions()(this.api),
            addons: require_addons()(this.api),
            settlements: require_settlements()(this.api),
            qrCode: require_qrCode()(this.api),
            fundAccount: require_fundAccount()(this.api),
            items: require_items()(this.api),
            cards: require_cards()(this.api),
            webhooks: require_webhooks()(this.api),
            documents: require_documents()(this.api),
            disputes: require_disputes()(this.api)
          });
        }
      }]);
      return Razorpay2;
    }();
    Razorpay.VERSION = pkg.version;
    module.exports = Razorpay;
  }
});
export default require_razorpay();
/*! Bundled license information:

axios/dist/browser/axios.cjs:
  (*! Axios v1.9.0 Copyright (c) 2025 Matt Zabriskie and contributors *)
*/
//# sourceMappingURL=razorpay.js.map
