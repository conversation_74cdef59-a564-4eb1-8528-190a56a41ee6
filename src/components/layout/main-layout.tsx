import { ReactNode } from 'react'
import { HorizontalNav } from "@/components/navigation/horizontal-nav"
import { useVisitorTracking } from "@/hooks/use-visitor-tracking"

interface MainLayoutProps {
  children: ReactNode
}

export function MainLayout({ children }: MainLayoutProps) {
  // Track visitor
  useVisitorTracking()

  return (
    <div className="min-h-screen bg-background">
      {/* Horizontal Navigation */}
      <HorizontalNav />

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        {children}
      </main>
    </div>
  )
}
