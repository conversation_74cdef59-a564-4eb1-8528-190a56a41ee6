import { Link, useLocation, useNavigate } from "react-router-dom"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Home,
  FileText,
  Palette,
  Mail,
  LogOut,
  LogIn,
  User,
  Settings,
  Shield,
  LayoutDashboard,
  Menu
} from "lucide-react"
import { useAuth } from "@/hooks/use-auth"
import { toast } from "sonner"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

const navigation = [
  {
    name: "Home",
    href: "/",
    icon: Home,
  },
  {
    name: "Templates",
    href: "/templates",
    icon: FileText,
  },
  {
    name: "Customize",
    href: "/customize",
    icon: Palette,
  },
  {
    name: "<PERSON>",
    href: "/contact",
    icon: Mail,
  },
]

interface HorizontalNavProps {
  className?: string
}

export function HorizontalNav({ className }: HorizontalNavProps) {
  const location = useLocation()
  const navigate = useNavigate()
  const { user, profile, loading, signOut } = useAuth()

  const handleSignOut = async () => {
    try {
      await signOut()
      toast.success('Signed out successfully')

      // Add a small delay before redirecting to allow state update to render
      setTimeout(() => {
        navigate('/')
      }, 50); // 50ms delay

    } catch (error) {
      console.error('Sign out error:', error)
      toast.error('Failed to sign out')
    }
  }

  // Filter navigation based on user role
  const getFilteredNavigation = () => {
    const nav = [...navigation]
    return nav
  }

  return (
    <nav className={cn("bg-card border-b", className)}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center gap-4">
            <Link href="/" className="flex items-center gap-2">
              <h1 className="text-xl font-bold">KaleidoneX</h1>
            </Link>
            {profile?.role === 'admin' && (
              <Badge variant="secondary" className="text-xs">
                <Shield className="h-3 w-3 mr-1" />
                Admin
              </Badge>
            )}
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-1">
            {getFilteredNavigation().map((item) => {
              const isActive = location.pathname === item.href
              return (
                <Link key={item.name} to={item.href}>
                  <Button
                    variant={isActive ? "secondary" : "ghost"}
                    size="sm"
                    className={cn(
                      "gap-2",
                      isActive && "bg-secondary"
                    )}
                  >
                    <item.icon className="h-4 w-4" />
                    {item.name}
                    {item.name === "Admin Panel" && (
                      <Badge variant="outline" className="ml-1 text-xs">
                        Admin
                      </Badge>
                    )}
                  </Button>
                </Link>
              )
            })}
          </div>

          {/* User section */}
          <div className="flex items-center space-x-4">
            {loading ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-muted border-t-primary rounded-full animate-spin" />
                <span className="text-sm text-muted-foreground">Loading...</span>
              </div>
            ) : user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <User className="h-4 w-4" />
                    </div>
                    <span className="hidden sm:block text-sm font-medium">
                      {profile?.full_name || user.email?.split('@')[0] || 'User'}
                    </span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <div className="flex items-center gap-2 p-2">
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <User className="h-4 w-4" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">
                        {profile?.full_name || user.email?.split('@')[0] || 'User'}
                      </p>
                      <p className="text-xs text-muted-foreground truncate">
                        {user.email}
                      </p>
                    </div>
                  </div>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link to="/dashboard" className="flex items-center gap-2">
                      <LayoutDashboard className="h-4 w-4" />
                      Dashboard
                    </Link>
                  </DropdownMenuItem>
                  {profile?.role === 'admin' && (
                    <DropdownMenuItem asChild>
                      <Link to="/admin" className="flex items-center gap-2">
                        <Shield className="h-4 w-4" />
                        Admin Panel
                      </Link>
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem asChild>
                    <Link to="/dashboard" className="flex items-center gap-2">
                      <Settings className="h-4 w-4" />
                      Settings
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={handleSignOut}
                    className="text-red-600 focus:text-red-700 focus:bg-red-50"
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    Sign Out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Link to="/login">
                <Button className="flex items-center gap-2">
                  <LogIn className="h-4 w-4" />
                  Sign In
                </Button>
              </Link>
            )}
          </div>

          {/* Mobile menu */}
          <div className="md:hidden">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-64">
                <div className="flex flex-col h-full">
                  {/* Mobile Logo */}
                  <div className="flex items-center gap-2 px-4 py-4 border-b">
                    <h1 className="text-xl font-bold">KaleidoneX</h1>
                    {profile?.role === 'admin' && (
                      <Badge variant="secondary" className="text-xs">
                        <Shield className="h-3 w-3 mr-1" />
                        Admin
                      </Badge>
                    )}
                  </div>

                  {/* Mobile Navigation */}
                  <nav className="flex-1 px-4 py-6 space-y-2">
                    {getFilteredNavigation().map((item) => {
                      const isActive = location.pathname === item.href
                      return (
                        <Link key={item.name} to={item.href}>
                          <Button
                            variant={isActive ? "secondary" : "ghost"}
                            className={cn(
                              "w-full justify-start gap-3",
                              isActive && "bg-secondary"
                            )}
                          >
                            <item.icon className="h-4 w-4" />
                            {item.name}
                            {item.name === "Admin Panel" && (
                              <Badge variant="outline" className="ml-auto text-xs">
                                Admin
                              </Badge>
                            )}
                          </Button>
                        </Link>
                      )
                    })}
                  </nav>

                  {/* Mobile User section */}
                  <div className="px-4 py-4 border-t">
                    {user ? (
                      <div className="space-y-2">
                        <div className="flex items-center gap-3 px-3 py-2 rounded-md bg-muted/50">
                          <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                            <User className="h-4 w-4" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">
                              {profile?.full_name || user.email?.split('@')[0] || 'User'}
                            </p>
                            <p className="text-xs text-muted-foreground truncate">
                              {user.email}
                            </p>
                          </div>
                        </div>
                        <div className="space-y-1">
                          <Link to="/dashboard">
                            <Button variant="ghost" size="sm" className="w-full justify-start gap-3">
                              <LayoutDashboard className="h-4 w-4" />
                              Dashboard
                            </Button>
                          </Link>
                          {profile?.role === 'admin' && (
                            <Link to="/admin">
                              <Button variant="ghost" size="sm" className="w-full justify-start gap-3">
                                <Shield className="h-4 w-4" />
                                Admin Panel
                              </Button>
                            </Link>
                          )}
                          <Link to="/dashboard">
                            <Button variant="ghost" size="sm" className="w-full justify-start gap-3">
                              <Settings className="h-4 w-4" />
                              Settings
                            </Button>
                          </Link>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="w-full justify-start gap-3 text-red-600 hover:text-red-700 hover:bg-red-50"
                            onClick={handleSignOut}
                          >
                            <LogOut className="h-4 w-4" />
                            Sign Out
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <Link to="/login">
                        <Button className="w-full justify-start gap-3">
                          <LogIn className="h-4 w-4" />
                          Sign In
                        </Button>
                      </Link>
                    )}
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </nav>
  )
}
