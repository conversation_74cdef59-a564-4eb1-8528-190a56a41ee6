import { createClient as createSupabaseClient } from '@supabase/supabase-js'
import { Database } from '../database.types'

export function createClient() {
  const url = import.meta.env.VITE_SUPABASE_URL
  const key = import.meta.env.VITE_SUPABASE_ANON_KEY

  if (!url || !key) {
    throw new Error('Missing Supabase environment variables. Please check your .env file.')
  }

  return createSupabaseClient<Database>(url, key)
}
