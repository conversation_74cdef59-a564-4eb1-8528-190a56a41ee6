import { useState, useEffect } from "react"
import { <PERSON> } from "react-router-dom"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  ArrowRight,
  Palette,
  FileText,
  Zap,
  Star,
  Users,
  Shield,
  Award,
  Download,
  Eye,
  ShoppingCart,
  Bookmark,
  ChevronRight,
  Sparkles,
  Play,
  CheckCircle,
  TrendingUp
} from "lucide-react"
import { createClient } from "@/lib/supabase/client"
import { Database } from "@/lib/database.types"
import { NewsletterSignup } from "@/components/newsletter-signup"

type Template = Database['public']['Tables']['templates']['Row']

export default function HomePage() {
  const [featuredTemplates, setFeaturedTemplates] = useState<Template[]>([])
  const [stats, setStats] = useState({
    templates: 50,
    users: 10000,
    downloads: 25000,
    rating: 4.9
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const handlePageShow = (event: PageTransitionEvent) => {
      if (event.persisted) {
        console.log('Page restored from bfcache, refetching data...')
        setLoading(true)
        loadFeaturedTemplates()
        loadStats()
      }
    };

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        console.log('Page became visible, refetching data...')
        loadFeaturedTemplates()
        loadStats()
      }
    };

    // Initial data fetch on mount
    loadFeaturedTemplates()
    loadStats()

    // Add event listeners
    window.addEventListener('pageshow', handlePageShow);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Listen for auth state changes to refetch data when user logs in/out
    const supabase = createClient()
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        console.log('Auth state change in home page:', event)
        if (event === 'SIGNED_IN' || event === 'SIGNED_OUT') {
          loadFeaturedTemplates()
          loadStats()
        }
      }
    )

    return () => {
      window.removeEventListener('pageshow', handlePageShow);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      subscription.unsubscribe()
    };
  }, [])

  const loadFeaturedTemplates = async () => {
    try {
      const supabase = createClient()

      console.log('Loading featured templates...')
      const { data, error } = await supabase
        .from('templates')
        .select('*')
        .limit(6)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Templates error:', error)
        throw error
      }

      console.log('Featured templates loaded:', data?.length || 0)
      setFeaturedTemplates(data || [])
    } catch (error) {
      console.error('Error loading featured templates:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const supabase = createClient()

      // Get template count
      const { count: templateCount } = await supabase
        .from('templates')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true)

      // Get user count
      const { count: userCount } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })

      // Get download count
      const { data: downloadData } = await supabase
        .from('templates')
        .select('downloads_count')
        .eq('is_active', true)

      const totalDownloads = downloadData?.reduce((sum, template) => sum + (template.downloads_count || 0), 0) || 0

      setStats({
        templates: templateCount || 50,
        users: userCount || 10000,
        downloads: totalDownloads || 25000,
        rating: 4.9
      })
    } catch (error) {
      console.error('Error loading stats:', error)
    }
  }

  return (
    <div className="space-y-16">
      {/* Enhanced Hero Section */}
      <div className="relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50 -z-10" />
        <div className="absolute top-0 left-1/4 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse" />
        <div className="absolute top-0 right-1/4 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000" />

        <div className="text-center space-y-8 py-20">
          <div className="space-y-6">
            <Badge variant="secondary" className="text-sm px-6 py-3 bg-gradient-to-r from-blue-100 to-purple-100 border-0 hover:scale-105 transition-transform">
              <Sparkles className="h-4 w-4 mr-2 text-blue-600" />
              New: {stats.templates}+ Premium Templates Available
            </Badge>

            <h1 className="text-6xl md:text-7xl lg:text-8xl font-bold tracking-tight">
              <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent">
                Welcome to
              </span>
              <br />
              <span className="bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 bg-clip-text text-transparent">
                KaleidoneX
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
              Create stunning, customizable templates with our powerful design tools.
              Build beautiful websites and applications with ease using our advanced customization engine.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <Link to="/templates">
              <Button size="lg" className="text-lg px-10 py-6 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                Browse Templates
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link to="/customize">
              <Button variant="outline" size="lg" className="text-lg px-10 py-6 border-2 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:scale-105">
                <Palette className="mr-2 h-5 w-5" />
                Start Customizing
              </Button>
            </Link>
            <a href="#demo">
              <Button variant="ghost" size="lg" className="text-lg px-6 py-6 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300">
                <Play className="mr-2 h-5 w-5" />
                Watch Demo
              </Button>
            </a>
          </div>

          {/* Enhanced Trust Indicators */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 pt-12 max-w-2xl mx-auto">
            <div className="flex flex-col items-center gap-2 p-4 rounded-lg bg-white/50 backdrop-blur-sm border border-white/20 hover:scale-105 transition-transform">
              <Star className="h-6 w-6 text-yellow-500 fill-current" />
              <span className="font-semibold text-lg">{stats.rating}/5</span>
              <span className="text-sm text-muted-foreground">Rating</span>
            </div>
            <div className="flex flex-col items-center gap-2 p-4 rounded-lg bg-white/50 backdrop-blur-sm border border-white/20 hover:scale-105 transition-transform">
              <Users className="h-6 w-6 text-blue-500" />
              <span className="font-semibold text-lg">{(stats.users / 1000).toFixed(0)}K+</span>
              <span className="text-sm text-muted-foreground">Users</span>
            </div>
            <div className="flex flex-col items-center gap-2 p-4 rounded-lg bg-white/50 backdrop-blur-sm border border-white/20 hover:scale-105 transition-transform">
              <Download className="h-6 w-6 text-green-500" />
              <span className="font-semibold text-lg">{(stats.downloads / 1000).toFixed(0)}K+</span>
              <span className="text-sm text-muted-foreground">Downloads</span>
            </div>
            <div className="flex flex-col items-center gap-2 p-4 rounded-lg bg-white/50 backdrop-blur-sm border border-white/20 hover:scale-105 transition-transform">
              <Shield className="h-6 w-6 text-purple-500" />
              <span className="font-semibold text-lg">100%</span>
              <span className="text-sm text-muted-foreground">Secure</span>
            </div>
          </div>
        </div>
      </div>

      {/* Featured Templates Section */}
      <div className="space-y-8">
        <div className="text-center space-y-4">
          <Badge variant="outline" className="px-4 py-2">
            <Award className="h-4 w-4 mr-2" />
            Featured Templates
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Popular Templates
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Discover our most popular and highly-rated templates, loved by thousands of users worldwide
          </p>
        </div>

        {loading ? (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="overflow-hidden animate-pulse">
                <div className="aspect-video bg-gray-200" />
                <CardContent className="p-6">
                  <div className="space-y-3">
                    <div className="h-4 bg-gray-200 rounded w-3/4" />
                    <div className="h-3 bg-gray-200 rounded w-full" />
                    <div className="h-3 bg-gray-200 rounded w-2/3" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {featuredTemplates.map((template) => (
              <Card key={template.id} className="group overflow-hidden hover:shadow-2xl transition-all duration-500 border-0 shadow-lg hover:scale-105">
                {/* Image Container with Overlay */}
                <div className="aspect-video bg-gradient-to-br from-blue-50 to-purple-50 relative overflow-hidden">
                  {template.preview_image ? (
                    <img
                      src={template.preview_image}
                      alt={template.title}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                  ) : (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center space-y-2">
                        <FileText className="h-12 w-12 text-blue-400 mx-auto" />
                        <p className="text-sm text-muted-foreground">Template Preview</p>
                      </div>
                    </div>
                  )}

                  {/* Overlay with Actions */}
                  <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center gap-3">
                    <Button size="sm" variant="secondary" className="bg-white/90 hover:bg-white">
                      <Eye className="h-4 w-4 mr-2" />
                      Preview
                    </Button>
                    <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                      <ShoppingCart className="h-4 w-4 mr-2" />
                      Buy Now
                    </Button>
                  </div>

                  {/* Price Badge */}
                  <div className="absolute top-4 right-4">
                    {template.original_price && template.original_price > template.price ? (
                      <div className="flex flex-col items-end gap-1">
                        <Badge className="bg-red-500 text-white hover:bg-red-600">
                          {template.discount_percentage}% OFF
                        </Badge>
                        <div className="bg-white/90 rounded px-2 py-1">
                          <div className="text-xs text-gray-500 line-through">₹{template.original_price}</div>
                          <div className="text-sm font-semibold text-gray-900">₹{template.price}</div>
                        </div>
                      </div>
                    ) : template.is_free ? (
                      <Badge className="bg-green-500 text-white hover:bg-green-600">
                        FREE
                      </Badge>
                    ) : (
                      <Badge className="bg-white/90 text-gray-900 hover:bg-white">
                        ₹{template.price}
                      </Badge>
                    )}
                  </div>

                  {/* Featured Badge */}
                  {template.is_featured && (
                    <div className="absolute top-4 left-4">
                      <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white border-0">
                        <Star className="h-3 w-3 mr-1" />
                        Featured
                      </Badge>
                    </div>
                  )}
                </div>

                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-xl font-semibold group-hover:text-blue-600 transition-colors">
                        {template.title}
                      </h3>
                      <p className="text-sm text-muted-foreground mt-2 line-clamp-2">
                        {template.description}
                      </p>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Eye className="h-4 w-4" />
                          <span>{template.views_count || 0}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Download className="h-4 w-4" />
                          <span>{template.downloads_count || 0}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                          <span>{template.rating || 0}</span>
                        </div>
                      </div>
                      <Button variant="ghost" size="sm" className="p-2">
                        <Bookmark className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        <div className="text-center">
          <Link to="/templates">
            <Button size="lg" variant="outline" className="px-8 py-3">
              View All Templates
              <ChevronRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </div>

      {/* Enhanced Features Grid */}
      <div className="space-y-8">
        <div className="text-center space-y-4">
          <h2 className="text-3xl font-bold">Why Choose KaleidoneX?</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Discover the powerful features that make KaleidoneX the best choice for your template needs
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center gap-2">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="h-6 w-6 text-blue-600" />
                </div>
                <CardTitle>50+ Premium Templates</CardTitle>
              </div>
              <CardDescription>
                Choose from a wide variety of professionally designed templates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Our template library includes designs for every industry and use case.
                From business websites to creative portfolios, find the perfect starting point.
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge variant="outline">Business</Badge>
                <Badge variant="outline">Portfolio</Badge>
                <Badge variant="outline">E-commerce</Badge>
                <Badge variant="outline">Blog</Badge>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center gap-2">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Palette className="h-6 w-6 text-purple-600" />
                </div>
                <CardTitle>Live Customization</CardTitle>
              </div>
              <CardDescription>
                Customize your templates in real-time with our visual editor
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                See your changes instantly as you customize colors, fonts, layouts, and content.
                No coding required - just point, click, and create.
              </p>
              <div className="flex items-center gap-2 text-sm text-green-600">
                <CheckCircle className="h-4 w-4" />
                <span>Real-time preview</span>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center gap-2">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Zap className="h-6 w-6 text-green-600" />
                </div>
                <CardTitle>Fast & Modern</CardTitle>
              </div>
              <CardDescription>
                Built with the latest technologies for optimal performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Powered by React, Tailwind CSS, and modern web standards.
                Your templates will be fast, responsive, and SEO-friendly.
              </p>
              <div className="flex items-center gap-2 text-sm text-blue-600">
                <TrendingUp className="h-4 w-4" />
                <span>99.9% Uptime</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Newsletter Signup */}
      <NewsletterSignup />
    </div>
  )
}
