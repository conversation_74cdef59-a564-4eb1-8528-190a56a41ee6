import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Palette, Layers, Settings, Code, Eye, Download } from "lucide-react"

export default function CustomizePage() {
  return (
    <div className="space-y-8">
      <div className="text-center space-y-4">
        <Badge variant="secondary" className="mb-4">
          <Palette className="h-4 w-4 mr-2" />
          Customization Studio
        </Badge>
        <h1 className="text-4xl md:text-5xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          Customize Your Template
        </h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Use our powerful visual editor to customize templates in real-time. No coding required!
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <div className="flex items-center gap-2">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Palette className="h-6 w-6 text-blue-600" />
              </div>
              <CardTitle>Visual Editor</CardTitle>
            </div>
            <CardDescription>
              Drag and drop interface for easy customization
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Customize colors, fonts, layouts, and content with our intuitive visual editor.
            </p>
            <Button className="w-full">Start Customizing</Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <div className="flex items-center gap-2">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Layers className="h-6 w-6 text-purple-600" />
              </div>
              <CardTitle>Component Library</CardTitle>
            </div>
            <CardDescription>
              Pre-built components ready to use
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Access hundreds of pre-built components and sections to enhance your template.
            </p>
            <Button variant="outline" className="w-full">Browse Components</Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <div className="flex items-center gap-2">
              <div className="p-2 bg-green-100 rounded-lg">
                <Eye className="h-6 w-6 text-green-600" />
              </div>
              <CardTitle>Live Preview</CardTitle>
            </div>
            <CardDescription>
              See changes in real-time
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Preview your changes instantly across different devices and screen sizes.
            </p>
            <Button variant="outline" className="w-full">Preview Template</Button>
          </CardContent>
        </Card>
      </div>

      <div className="text-center">
        <p className="text-muted-foreground mb-4">
          Customization feature coming soon! Stay tuned for updates.
        </p>
        <Button size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
          <Download className="mr-2 h-5 w-5" />
          Download Template
        </Button>
      </div>
    </div>
  )
}
